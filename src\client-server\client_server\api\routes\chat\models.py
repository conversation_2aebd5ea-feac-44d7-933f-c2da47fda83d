"""
Chat data models and request/response schemas.

This module contains all Pydantic models used for chat functionality,
including request models, response chunks, and action definitions.
"""

from typing import Any, Literal
from pydantic import BaseModel


class ChatStreamRequest(BaseModel):
    """Request model for HTTP streaming chat"""
    mode: Literal["NORMAL", "ECO", "PRO"] = "NORMAL"
    messages: list[dict[str, Any]] = []
    provider: str | dict[str, Any] = ""
    conversation_id: str = ""
    session__: str | None = None
    request_id: str = ""
    web_search: bool = False
    provide_followups: bool = True


class ChatStreamChunkAction(BaseModel):
    id: str
    name: str
    type: Literal["function"]
    args: dict[str, Any]


class ChatStreamChunk(BaseModel):
    type: Literal["content", "action"]
    memory_id: str
    chunk_index: int = 0


class ChatStreamActionChunk(ChatStreamChunk):
    type: Literal["action"]
    action: list[ChatStreamChunkAction]


class ChatStreamContentChunk(ChatStreamChunk):
    type: Literal["content"]
    content: str
