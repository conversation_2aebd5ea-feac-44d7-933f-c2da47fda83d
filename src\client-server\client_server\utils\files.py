import os
import mimetypes


def is_text_file(file_path, blocksize=512):
    """
    Check if a file is a text file.

    Tries to read a small portion of the file and check for non-text characters.
    """
    text_characters = bytearray({7, 8, 9, 10, 12, 13, 27} | set(range(0x20, 0x100)))
    try:
        with open(file_path, "rb") as file:
            block = file.read(blocksize)
            if not block:
                return False  # Empty file
            if b"\x00" in block:
                return False  # File contains null byte
            non_text = block.translate(None, text_characters)
            if float(len(non_text)) / len(block) > 0.30:
                return False  # More than 30% non-text characters
            else:
                return True
    except Exception:
        return False  # If any error occurs, treat as non-text


def get_files(data=None):
    ## This function is responsible for getting all the files from a given directory.
    """
    To make this work, we will be considering some special cases like:
    - Skipping directories like python virtual environments, node_modules, __pycache__, conda environments, etc.
    - Skipping files like .gitignore, .git, .DS_Store, .env, .env.local, .env.production.local, etc.
    - Skipping non-relatable files like images and more.
    """

    """Get all text-readable files from a given directory."""
    path = data["root"]
    # Directories to exclude from traversal
    excluded_dirs = {
        ".git",
        "node_modules",
        "venv",
        ".venv",
        "env",
        ".env",
        "__pycache__",
        "build",
        "dist",
        ".idea",
        ".vscode",
        ".pytest_cache",
        ".mypy_cache",
        ".tox",
        "coverage",
        "logs",
        "tmp",
        "temp",
        ".cache",
        ".conda",
        ".Rproj.user",
        ".m2",
    }

    excluded_files = {
        ".gitignore",
        ".gitattributes",
        ".gitmodules",
        ".DS_Store",
        ".env",
        ".env.local",
        ".env.production.local",
    }

    excluded_extensions = {
        ".png",
        ".jpg",
        ".jpeg",
        ".gif",
        ".svg",
        ".bmp",
        ".tiff",
        ".ico",
        ".mp4",
        ".mkv",
        ".avi",
        ".mov",
        ".wmv",
        ".mp3",
        ".wav",
        ".aac",
        ".flac",
        ".ogg",
        ".zip",
        ".tar",
        ".gz",
        ".rar",
        ".7z",
        ".exe",
        ".dll",
        ".so",
        ".bin",
        ".app",
        ".sh",
        ".class",
        ".o",
        ".a",
        ".lib",
        ".pyd",
        ".whl",
        ".db",
        ".sqlite",
        ".mdb",
        ".jsonl",
    }

    # Maximum file size to consider (e.g., 100 MB)
    max_file_size = 100 * 1024 * 1024  # 100 MB

    # List of MIME types to check against
    text_mime_types = {
        "text/plain",
        "text/html",
        "text/css",
        "text/javascript",
        "application/javascript",
        "application/json",
        "application/xml",
        "text/markdown",
        "text/x-python",
        "text/x-c",
        "text/x-c++",
        "text/x-java-source",
        "text/x-shellscript",
        "application/x-shellscript",
        "text/x-ruby",
        "text/x-php",
        "text/x-go",
        "text/x-rust",
        "text/x-typescript",
        "text/x-yaml",
        "text/x-toml",
        "text/x-sql",
        "text/x-kotlin",
        "text/x-swift",
        "text/x-scala",
        "application/x-yaml",
        "text/csv",
        "text/tab-separated-values",
        "text/x-diff",
        "text/x-patch",
    }

    all_files = []
    for root, dirs, files in os.walk(path, topdown=True):
        # Exclude specified directories and hidden directories
        dirs[:] = [d for d in dirs if d not in excluded_dirs and not d.startswith(".")]

        for file in files:
            # Exclude hidden files
            if (
                file.startswith(".")
                or file in excluded_files
                or os.path.splitext(file)[1] in excluded_extensions
            ):
                continue

            file_path = os.path.join(root, file)

            # Check if file is too large
            try:
                if os.path.getsize(file_path) > max_file_size:
                    continue  # Skip large files
            except (OSError, PermissionError):
                continue  # Skip files we can't access

            # Check if file is readable
            if not os.access(file_path, os.R_OK):
                continue  # Skip unreadable files

            # Guess MIME type based on file extension
            mime_type, _ = mimetypes.guess_type(file_path)

            if mime_type in text_mime_types:
                all_files.append(
                    {
                        "path": file_path,
                        "name": (
                            file_path.split("/")[-1]
                            if "/" in file_path
                            else file_path.split("\\")[-1]
                        ),
                    }
                )
                continue  # MIME type matched, no need to read file

            # If MIME type is not conclusive, attempt to read part of the file to check if it's text
            try:
                if is_text_file(file_path):
                    all_files.append(
                        {
                            "path": file_path,
                            "name": (
                                file_path.split("/")[-1]
                                if "/" in file_path
                                else file_path.split("\\")[-1]
                            ),
                        }
                    )
            except (OSError, PermissionError):
                continue  # Skip files we can't read

    return all_files
