import asyncio
import json
import time
import traceback
from typing import Any, As<PERSON><PERSON>enerator, Literal
import os
from fastapi import Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import requests
import tqdm
from client_server.utils.models.knowledgebase import QdrantKnowledgeBase

from client_server.utils.actions.swagger_search import (
    _perform_swagger_search,
    process_swagger_search,
)
from client_server.utils.actions.context_search import (
    process_context_search,
    _perform_local_search,
)
from client_server.utils.actions.folder_search import (
    _perform_folder_search,
    process_folder_search,
)
from client_server.services.tokenization.utils import (
    TokenizationBuilder,
    ensure_messages_within_token_limit,
)
from client_server.utils.model_selector import ModelSelector
from client_server.api.events.connection import update_session_activity
from client_server.utils.actions.web_search import process_web_search
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import (
    log_memory_usage,
    log_file_stats,
    log_execution_time,
    log_api_call_stats,
    log_system_info,
    log_operation_stats,
)
from client_server.utils.platform_detector import PlatformDetector
from client_server.core.state import G_BASE_URL, G_CANCELLED_REQUEST_IDS
from . import route
from client_server.utils.actions.utils import SearchReferences
from client_server.core.constants import SSL_CERT_FILE
from uuid import uuid4
from client_server.services.dependencies.ollama import OllamaDependency
from client_server.utils.path_selector import PathSelector
from client_server.services.dependencies import DependencyStatus
from client_server.utils.router.litellm_router import get_litellm_session_router

# Import for ECO mode (local inference) - only used in process_local_chat_request_http
try:
    from client_server.services.inference.utils import InferenceBuilder
except ImportError:
    InferenceBuilder = None
    LOGGER.warning("InferenceBuilder not available - ECO mode may not work")


MESSAGE_DELIMITER = "<__!!__END__!!__>"


# -----------------------------------------------------------------------------
# HTTP Event Emitter for Streaming
# -----------------------------------------------------------------------------


class HTTPStreamEventEmitter:
    """
    Replaces socketio.emit functionality with Server-Sent Events (SSE) for HTTP streaming.
    Formats events as SSE data that can be streamed to the client.
    """

    def __init__(self, request_id: str):
        self.request_id = request_id
        self.events = []

    async def emit(self, event_name: str, data: dict[str, Any], to: str = None):
        """
        Emit an event by adding it to the events list.
        This replaces the socketio.emit functionality.
        """
        event_data = {
            "event": event_name,
            "data": data,
            "timestamp": time.time()
        }
        self.events.append(event_data)

    def format_as_sse(self, event_data: dict[str, Any]) -> str:
        """Format event data as Server-Sent Events (SSE) format"""
        event_name = event_data.get("event", "message")
        data = event_data.get("data", {})

        # Format as SSE
        sse_data = f"event: {event_name}\ndata: {json.dumps(data)}\n\n"
        return sse_data

    def get_events(self) -> list[dict[str, Any]]:
        """Get all accumulated events"""
        return self.events.copy()

    def clear_events(self):
        """Clear all accumulated events"""
        self.events.clear()



class ChatStreamRequest(BaseModel):
    """Request model for HTTP streaming chat"""
    mode: Literal["NORMAL", "ECO", "PRO"] = "NORMAL"
    messages: list[dict[str, Any]] = []
    provider: str | dict[str, Any] = ""
    conversation_id: str = ""
    session__: str | None = None
    request_id: str = ""
    web_search: bool = False
    provide_followups: bool = True


class ChatStreamChunkAction(BaseModel):
    id: str
    name: str
    type: Literal["function"]
    args: dict[str, Any]


class ChatStreamChunk(BaseModel):
    type: Literal["content", "action"]
    memory_id: str
    chunk_index: int = 0
    # action: dict[str, any] = {}


class ChatStreamActionChunk(ChatStreamChunk):
    type: Literal["action"]
    action: list[ChatStreamChunkAction]

class ChatStreamContentChunk(ChatStreamChunk):
    type: Literal["content"]
    content: str


# -----------------------------------------------------------------------------
# Tool Calling System for LiteLLM Integration
# -----------------------------------------------------------------------------

class ToolCallHandler:
    """
    Handles tool calls from LLM responses and routes them to appropriate action handlers.
    Integrates with the existing action system while providing LLM tool calling interface.
    """

    def __init__(self, event_emitter: HTTPStreamEventEmitter, session_id: str, request_id: str):
        self.event_emitter = event_emitter
        self.session_id = session_id
        self.request_id = request_id
        self.search_references = SearchReferences(request_id)

    @staticmethod
    def get_tool_schemas() -> list[dict[str, Any]]:
        """
        Define tool schemas that the LLM can understand and call.
        Maps to existing action handlers.
        """
        return [
            {
                "type": "function",
                "function": {
                    "name": "context_search",
                    "description": "Search for relevant information within a specific knowledge base using semantic search",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query to find relevant information"
                            },
                            "kbid": {
                                "type": "string",
                                "description": "The knowledge base ID to search within"
                            }
                        },
                        "required": ["query", "kbid"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "folder_search",
                    "description": "Search for relevant information within a specific folder of a knowledge base",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query to find relevant information"
                            },
                            "folder_path": {
                                "type": "string",
                                "description": "The folder path to search within"
                            },
                            "kbid": {
                                "type": "string",
                                "description": "The knowledge base ID containing the folder"
                            }
                        },
                        "required": ["query", "folder_path", "kbid"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "Search the web for current information and external resources",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query for web search"
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "swagger_search",
                    "description": "Search API documentation and endpoints using Swagger/OpenAPI specifications",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "The search query to find relevant API endpoints"
                            },
                            "kbid": {
                                "type": "string",
                                "description": "The knowledge base ID containing the Swagger documentation"
                            }
                        },
                        "required": ["query", "kbid"]
                    }
                }
            }
        ]

    async def execute_tool_call(self, tool_call: dict[str, Any]) -> tuple[dict[str, Any], bool]:
        """
        Execute a tool call and return the result.

        Args:
            tool_call: Tool call from LLM response

        Returns:
            Tuple of (tool_result, needs_followup)
        """
        function_name = tool_call["function"]["name"]
        function_args = json.loads(tool_call["function"]["arguments"])
        tool_id = tool_call["id"]

        LOGGER.info(f"Executing tool call: {function_name} with args: {function_args}")

        try:
            # Emit searching event before tool execution
            await self.event_emitter.emit(
                "chat_response_searching",
                data={"request_id": self.request_id, "action_id": tool_id}
            )

            # Route to appropriate action handler
            if function_name == "context_search":
                result, search_refs = await process_context_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    kbid=function_args["kbid"],
                    search_references=self.search_references
                )

            elif function_name == "folder_search":
                result, search_refs = await process_folder_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    folder_path=function_args["folder_path"],
                    index_name=function_args["kbid"],
                    search_references=self.search_references
                )

            elif function_name == "web_search":
                result, search_refs = await process_web_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    session=self.session_id,
                    search_references=self.search_references
                )

            elif function_name == "swagger_search":
                result, search_refs = await process_swagger_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    kbid=function_args["kbid"],
                    search_refrences=self.search_references
                )

            else:
                raise ValueError(f"Unknown tool function: {function_name}")

            # Update search references
            self.search_references = search_refs

            # Emit search references if available
            if search_refs and search_refs.get_search_result().get("results"):
                await self.event_emitter.emit(
                    "chat_response_references",
                    data=search_refs.get_search_result()
                )

            # Format tool result for LLM
            tool_result = {
                "tool_call_id": tool_id,
                "content": json.dumps(result)
            }

            # Determine if LLM follow-up is needed
            needs_followup = self._needs_llm_followup(function_name, function_args, result)

            return tool_result, needs_followup

        except Exception as e:
            LOGGER.error(f"Error executing tool call {function_name}: {e}")
            error_result = {
                "tool_call_id": tool_id,
                "content": json.dumps({
                    "error": str(e),
                    "status": "error"
                })
            }
            return error_result, True  # Error requires follow-up

    def _needs_llm_followup(self, function_name: str, args: dict[str, Any], result: Any) -> bool:
        """
        Determine if a tool call result needs LLM follow-up processing.
        """
        # Always need follow-up for tool calls to process and respond to results
        return True


def format_filepath(filepath: str):
    """Format file path based on platform"""
    if PlatformDetector.is_macos():
        formatted_path = f"/{filepath}"
        LOGGER.debug(f"Formatted macOS path: {filepath} -> {formatted_path}")
        return formatted_path
    LOGGER.debug(f"Using original path: {filepath}")
    return filepath

def ensure_eco_mode_dependencies():
    """Ensure Ollama dependencies are installed for ECO mode"""
    dependency_start = time.time()
    LOGGER.info("Starting Ollama dependency setup for ECO mode")
    log_memory_usage("before_ollama_setup")

    ollama_dependency = OllamaDependency(PathSelector.get_cache_path() / "ollama")

    # Create a progress bar for installation progress
    pbar = None
    installation_start = None

    try:
        status_count = 0
        for status, data in ollama_dependency.ensure():
            status_count += 1

            if status == DependencyStatus.READY:
                LOGGER.info(f"Ollama ready: {data}")
                continue

            if status == DependencyStatus.INSTALLED:
                if installation_start:
                    install_time = time.time() - installation_start
                    LOGGER.info(
                        f"Ollama installed successfully in {install_time:.2f}s: {data}"
                    )
                else:
                    LOGGER.info(f"Ollama installed: {data}")
                continue

            if status == DependencyStatus.ERROR:
                LOGGER.error(f"Ollama error: {data}")
                continue

            if status == DependencyStatus.MISSING:
                LOGGER.warning(f"Ollama missing: {data}")
                continue

            if status == DependencyStatus.INSTALLING:
                if installation_start is None:
                    installation_start = time.time()
                    LOGGER.info("Starting Ollama installation")

                # Handle progress updates
                if isinstance(data, float):
                    if pbar is None:
                        pbar = tqdm.tqdm(total=100, desc="Installing Ollama")
                    pbar.n = round(data)
                    pbar.refresh()

                    if round(data) % 10 == 0:  # Log every 10%
                        LOGGER.debug(f"Ollama installation progress: {data:.1f}%")
                else:
                    LOGGER.info(f"Ollama installation: {data}")
                continue

            # Handle other status messages
            if isinstance(data, str):
                LOGGER.debug(f"Ollama status {status}: {data}")

    except Exception as e:
        LOGGER.error(f"Error during Ollama dependency setup: {e}")
        LOGGER.error(f"Ollama setup error traceback: {traceback.format_exc()}")
        raise
    finally:
        if pbar is not None:
            pbar.close()

    dependency_time = time.time() - dependency_start
    log_memory_usage("after_ollama_setup")
    LOGGER.info(f"Ollama dependency setup completed in {dependency_time:.2f}s")





async def process_local_chat_request_http(
    event_emitter: HTTPStreamEventEmitter,
    payload: ChatStreamRequest
) -> AsyncGenerator[str, None]:
    """
    HTTP streaming version of process_local_chat_request.
    Process chat request locally using ECO mode with HTTP streaming.
    """
    overall_start_time = time.time()
    log_memory_usage("http_local_chat_start")

    LOGGER.info(
        f"Processing HTTP local chat request - "
        f"Request ID: {payload.request_id}, Messages: {len(payload.messages)}"
    )

    sequence_messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant developed by CodeMate AI that can answer questions and help with tasks.",
        }
    ]
    search_results = {
        "request_id": payload.request_id,
        "results": [],
    }

    try:
        message_processing_start = time.time()
        total_contexts = sum(len(message.context) for message in payload.messages)
        LOGGER.info(f"Processing {total_contexts} contexts in local mode")

        for msg_idx, message in enumerate(payload.messages):
            LOGGER.debug(
                f"Processing message {msg_idx+1}/{len(payload.messages)}: {message.role}"
            )

            if message.role == "user":
                for ctx_idx, context in enumerate(message.context):
                    context_start = time.time()
                    LOGGER.debug(
                        f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type}"
                    )

                    formatted_context = ""
                    additional_content = ""

                    if context.type == "file":
                        if context.path:
                            try:
                                file_size = log_file_stats(context.path)
                                read_start = time.time()

                                with open(context.path, "r", encoding="utf-8") as e:
                                    context.content = e.read()

                                read_time = time.time() - read_start
                                content_length = len(context.content)
                                LOGGER.debug(
                                    f"File read - Path: {context.path}, Size: {file_size} bytes, "
                                    f"Content: {content_length} chars, Read time: {read_time:.2f}s"
                                )
                            except Exception as e:
                                LOGGER.error(f"Failed to read file {context.path}: {e}")
                                context.content = ""

                        formatted_context = (
                            f"File Name: {context.name}\n"
                            f"File Path: {context.path}\n"
                            f"File Content:\n{context.content}\n\n"
                        )

                    elif context.type == "terminal":
                        formatted_context = f"Terminal:\n{context.content}\n\n"

                    elif context.type == "warnings":
                        formatted_context = f"Warnings:\n{context.content}\n\n"

                    elif context.type == "errors":
                        formatted_context = f"Errors:\n{context.content}\n\n"

                    elif context.type == "commit":
                        formatted_context = (
                            f"COMMIT INFORMATION:\n{context.content}\n\n"
                        )

                    elif context.type == "folder":
                        LOGGER.info(
                            f"Processing folder context - Path: {context.path}, KBID: {context.kbid}"
                        )

                        if not context.path:
                            LOGGER.warning(f"Ignoring folder without path: {context}")
                            continue

                        if QdrantKnowledgeBase.exists_id(context.kbid or ""):
                            LOGGER.info("Knowledge base found for folder search")
                            # Emit searching event before folder search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()

                            folder_contexts = await _perform_folder_search(
                                query=message.content.replace(
                                    f"<__$__{context.id}__$__>", context.path or ""
                                ),
                                index_name=context.kbid or "",
                                folder_path=context.path or "",
                                is_local=True,
                            )

                            search_time = time.time() - search_start
                            LOGGER.info(
                                f"Folder search completed - Found {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                text = folder_context["content"]["text"]
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                additional_content += f"FileName: {_filename} | FilePath: {_filepath} \n {text}\n"
                        else:
                            LOGGER.warning(
                                f"Knowledge base not found for ID: {context.kbid}"
                            )
                        print("Emitting folder search results")
                        await event_emitter.emit(
                            "chat_response_references", data=search_results
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": search_results
                        })
                        print("Folder search results emitted to client")
                        await asyncio.sleep(0.001)

                    elif context.type in ["docs", "codebase", "git"]:
                        LOGGER.info(
                            f"Processing {context.type} context - Name: {context.name}, KBID: {context.kbid}"
                        )

                        formatted_context = f"{context.type} {context.name}"
                        additional_content = (
                            f"\n\n[ Attached Knowledgebases:\n"
                            f"Name: {context.name}    |    KNOWLEDGEBASE ID: {context.kbid}]\n\n"
                        )

                        search_query = message.content.replace(
                            f"<__$__{context.id}__$__>",
                            context.path or context.name or "",
                        )

                        if context.type == "codebase":
                            # Emit searching event before codebase search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            folder_contexts = await _perform_folder_search(
                                query=search_query,
                                index_name=context.kbid or "",
                                folder_path="",
                                is_local=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Codebase search completed - {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                text = folder_context["content"]["text"]
                                additional_content += text + "\n\n"

                            print("Emitting codebase search results")
                            await event_emitter.emit(
                                "chat_response_references", data=search_results
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_references",
                                "data": search_results
                            })
                            print("Codebase search results emitted to client")
                            await asyncio.sleep(0.001)

                        elif context.type == "docs":
                            # Emit searching event before docs search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            docs_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context.kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Docs search completed - {len(docs_contexts)} results in {search_time:.2f}s"
                            )

                            for docs_context in docs_contexts:
                                text = docs_context["content"]["text"]
                                additional_content += text + "\n\n"

                        elif context.type == "github":
                            # Emit searching event before git search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            git_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context.kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.info(
                                f"GitHub search completed - {len(git_contexts)} results in {search_time:.2f}s"
                            )
                            LOGGER.debug(
                                f"First GitHub context: {git_contexts[0] if git_contexts else 'None'}"
                            )

                            for git_context in git_contexts:
                                text = git_context["content"]["text"]
                                additional_content += text + "\n\n"

                    # Replace context placeholders in message content
                    message.content = message.content or ""
                    message.content = (
                        message.content.replace(
                            f"<__$__{context.id}__$__>", formatted_context
                        )
                        + additional_content
                    )

                    context_time = time.time() - context_start
                    LOGGER.debug(
                        f"Context {context.type} processed in {context_time:.2f}s"
                    )

                sequence_messages.append(
                    {
                        "role": "user",
                        "content": message.content,
                    }
                )

                content_length = len(message.content)
                LOGGER.debug(f"Added user message with {content_length} characters")
            else:
                sequence_messages.append(
                    {"role": "assistant", "content": message.content}
                )
                LOGGER.debug(f"Added assistant message")

        message_processing_time = time.time() - message_processing_start
        LOGGER.info(f"Message processing completed in {message_processing_time:.2f}s")

        # Apply token limiting to fit within the 70k token limit
        token_limiting_start = time.time()
        original_message_count = len(sequence_messages)

        sequence_messages = ensure_messages_within_token_limit(
            tokenizer=TokenizationBuilder.create(),
            messages=sequence_messages,
            max_tokens=70000,
            model=ModelSelector.chat(),
        )

        token_limiting_time = time.time() - token_limiting_start
        final_message_count = len(sequence_messages)

        LOGGER.info(
            f"Token limiting completed - Original: {original_message_count} messages, "
            f"Final: {final_message_count} messages, Time: {token_limiting_time:.2f}s"
        )

        if original_message_count != final_message_count:
            LOGGER.warning(
                f"Truncated {original_message_count - final_message_count} messages due to token limit"
            )

    except Exception as e:
        LOGGER.error(f"Error during HTTP local chat message processing: {e}")
        LOGGER.error(f"HTTP local chat processing error traceback: {traceback.format_exc()}")

        await event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        })
        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })
        return

    try:
        inference_start = time.time()
        LOGGER.info("Starting local LLM inference")
        log_memory_usage("before_local_inference")

        llm_backend = InferenceBuilder.create()
        selected_model = ModelSelector.chat()
        LOGGER.info(f"Using local model: {selected_model}")

        llm_response = llm_backend.stream(
            model=selected_model,
            messages=sequence_messages,
        )

        chat_id = str(uuid4())
        chunk_count = 0
        total_response_length = 0

        LOGGER.info(f"Starting response streaming with chat ID: {chat_id}")

        for i, chunk in enumerate(llm_response):
            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, skipping chunk")
                break

            if len(chunk.choices) <= 0:
                continue

            if chunk.choices[0].finish_reason is not None:
                LOGGER.info(
                    f"Stream finished with reason: {chunk.choices[0].finish_reason}"
                )
                break

            if chunk.choices[0].delta.content is not None:
                chunk_count += 1
                content = chunk.choices[0].delta.content
                total_response_length += len(content)

                if chunk_count <= 5:  # Log first few chunks
                    LOGGER.debug(f"Chunk {chunk_count}: {len(content)} chars")

                response_data = {
                    "type": "content",
                    "request_id": payload.request_id,
                    "conversation_id": "",
                    "data": {
                        "type": "content",
                        "memory_id": chat_id,
                        "chunk_index": i,
                        "content": content,
                    },
                }

                await event_emitter.emit("chat_response", data=response_data)
                yield event_emitter.format_as_sse({
                    "event": "chat_response",
                    "data": response_data
                })

        inference_time = time.time() - inference_start
        avg_time_per_chunk = inference_time / chunk_count if chunk_count > 0 else 0

        LOGGER.info(
            f"Local inference completed - Chunks: {chunk_count}, "
            f"Total response: {total_response_length} chars, "
            f"Time: {inference_time:.2f}s, "
            f"Avg per chunk: {avg_time_per_chunk:.3f}s"
        )

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("http_local_chat_complete")

        LOGGER.info(f"HTTP local chat request completed successfully in {overall_time:.2f}s")

    except Exception as e:
        inference_time = (
            time.time() - inference_start if "inference_start" in locals() else 0
        )
        LOGGER.error(
            f"Error during HTTP local chat inference after {inference_time:.2f}s: {e}"
        )
        LOGGER.error(f"HTTP local inference error traceback: {traceback.format_exc()}")

        error_response = {
            "request_id": payload.request_id,
            "error": "An error occurred while processing the chat request.",
        }
        await event_emitter.emit("chat_response_error", data=error_response)
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": error_response
        })
        raise


async def _handle_action_http(
    action: ChatStreamChunkAction,
    search_references: SearchReferences,
    session: str,
    event_emitter: HTTPStreamEventEmitter,
    request_id: str,
):
    """
    HTTP version of _handle_action.
    Route and handle different types of actions for HTTP streaming.
    """
    start_time = time.time()
    LOGGER.info(f"Handling action: {action.name} with ID: {action.id}")
    LOGGER.debug(f"Action args: {action.args}")

    try:
        # Emit searching event before any search happens
        await event_emitter.emit(
            "chat_response_searching",
            data={"request_id": request_id, "action_id": action.id}
        )

        match action.name:
            case "context_search":
                LOGGER.debug(
                    f"Processing context search - Query: {action.args.get('query', '')[:100]}..."
                )
                result = await process_context_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    kbid=action.args["kbid"],
                    search_references=search_references,
                )

            case "folder_search":
                LOGGER.debug(
                    f"Processing folder search - Path: {action.args.get('folder_path', '')}"
                )
                result = await process_folder_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    folder_path=action.args["folder_path"],
                    index_name=action.args["kbid"],
                    search_references=search_references,
                )

            case "web_search":
                LOGGER.debug(
                    f"Processing web search - Query: {action.args.get('query', '')[:100]}..."
                )
                result = await process_web_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    session=session,
                    search_references=search_references,
                )

            case "swagger_search":
                LOGGER.debug(
                    f"Processing swagger search - KBID: {action.args.get('kbid', '')}"
                )
                result = await process_swagger_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    search_refrences=search_references,
                    kbid=action.args["kbid"],
                )

            case _:
                LOGGER.warning(f"Unknown action type: {action.name}")
                raise ValueError(f"Unknown action type: {action.name}")

        execution_time = log_execution_time(
            f"_handle_action_http({action.name})", start_time
        )
        LOGGER.debug(f"Action {action.name} completed successfully")
        return result

    except Exception as e:
        execution_time = time.time() - start_time
        LOGGER.error(
            f"Error handling action {action.name} after {execution_time:.2f}s: {e}"
        )
        LOGGER.error(f"Action error traceback: {traceback.format_exc()}")
        raise




async def _process_action_chunk_http(
    chunk: str,
    search_references: SearchReferences,
    session: str,
    event_emitter: HTTPStreamEventEmitter,
    request_id: str,
):
    """HTTP version of _process_action_chunk"""
    start_time = time.time()
    LOGGER.debug(f"Processing action chunk - Length: {len(chunk)} characters")

    try:
        chunk_data = ChatStreamActionChunk.model_validate_json(chunk)
        LOGGER.debug(
            f"Action chunk memory_id: {chunk_data.memory_id}, actions count: {len(chunk_data.action)}"
        )

        chunk_message_list = []
        references = []

        for i, chnk_data in enumerate(chunk_data.action):
            LOGGER.debug(
                f"Processing action {i+1}/{len(chunk_data.action)}: {chnk_data.name}"
            )
            action_start = time.time()

            data, ref = await _handle_action_http(
                chnk_data,
                search_references=search_references,
                session=session,
                event_emitter=event_emitter,
                request_id=request_id,
            )
            action_time = time.time() - action_start
            LOGGER.debug(f"Action {chnk_data.name} processed in {action_time:.2f}s")

            chunk_message_list.append(data)
            references.append(ref)

        response = [
            {
                "role": "assistant",
                "action": list(map(lambda x: x[0]["action"], chunk_message_list)),
            },
            {
                "role": "action",
                "content": list(map(lambda x: x[1]["content"], chunk_message_list)),
                "action_id": list(map(lambda x: x[1]["action_id"], chunk_message_list)),
            },
        ]

        log_execution_time("_process_action_chunk_http", start_time)
        LOGGER.debug(
            f"Action chunk processing completed - Generated {len(response)} response messages"
        )

        return response, references[0] if references else None

    except json.JSONDecodeError as e:
        LOGGER.error(f"Failed to parse action chunk: {chunk[:200]}...")
        LOGGER.error(f"JSON decode error: {e}")
        raise
    except Exception as e:
        LOGGER.error(f"Error processing action chunk: {e}")
        LOGGER.error(f"Action chunk error traceback: {traceback.format_exc()}")
        raise


async def _process_content_chunk_http(
    chunk: str,
    search_references: SearchReferences,
    session: str,
    event_emitter: HTTPStreamEventEmitter,
    request_id: str,
):
    """HTTP version of _process_content_chunk"""
    start_time = time.time()
    LOGGER.debug(f"Processing content chunk - Length: {len(chunk)} characters")

    try:
        chunk_data = ChatStreamContentChunk.model_validate_json(chunk)
        LOGGER.debug(
            f"Content chunk - Memory ID: {chunk_data.memory_id}, "
            f"Index: {chunk_data.chunk_index}, Content length: {len(chunk_data.content)}"
        )

        result = chunk_data.model_dump(), []
        log_execution_time("_process_content_chunk_http", start_time)
        return result

    except json.JSONDecodeError as e:
        LOGGER.error(f"Failed to parse content chunk: {chunk[:200]}...")
        LOGGER.error(f"JSON decode error: {e}")
        raise
    except Exception as e:
        LOGGER.error(f"Error processing content chunk: {e}")
        LOGGER.error(f"Content chunk error traceback: {traceback.format_exc()}")
        raise



async def process_chat_request_from_server_http(
    event_emitter: HTTPStreamEventEmitter,
    payload: ChatStreamRequest,
) -> AsyncGenerator[str, None]:
    """
    HTTP streaming version of process_chat_request_from_server.
    Yields Server-Sent Events instead of using WebSocket emissions.
    """
    overall_start_time = time.time()
    log_memory_usage("http_chat_request_start")

    LOGGER.info(
        f"Processing HTTP chat request from server - "
        f"Request ID: {payload.request_id}, Mode: {payload.mode}"
    )
    LOGGER.info(
        f"Message count: {len(payload.messages)}, "
        f"Web search enabled: {payload.web_search}, "
        f"Conversation ID: {payload.conversation_id}"
    )

    # Log system information
    system_info = log_system_info()

    context_processing_start = time.time()
    total_contexts = sum(len(message.context) for message in payload.messages)
    LOGGER.info(f"Processing {total_contexts} contexts across all messages")

    for msg_idx, message in enumerate(payload.messages):
        LOGGER.debug(
            f"Processing message {msg_idx+1}/{len(payload.messages)} with {len(message.context)} contexts"
        )

        for ctx_idx, context in enumerate(message.context):
            context_start = time.time()
            LOGGER.debug(
                f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type} - {context.name}"
            )

            if context.type == "file":
                if context.path:
                    try:
                        file_path = format_filepath(context.path)
                        file_size = log_file_stats(file_path)

                        read_start = time.time()
                        with open(file_path, "r", encoding="utf-8") as e:
                            context.content = e.read()
                        read_time = time.time() - read_start

                        content_length = len(context.content)
                        content_lines = context.content.count("\n") + 1
                        LOGGER.debug(
                            f"File read - Path: {file_path}, Size: {file_size} bytes, "
                            f"Content: {content_length} chars, {content_lines} lines, "
                            f"Read time: {read_time:.2f}s"
                        )
                    except Exception as e:
                        LOGGER.error(f"Failed to read file {context.path}: {e}")
                        context.content = ""
                else:
                    LOGGER.warning(f"File context without path: {context.name}")

            elif context.type == "swagger":
                LOGGER.info(f"Processing swagger context - KBID: {context.kbid}")

                base_url = G_BASE_URL.get().general

                try:
                    # Emit searching event before swagger search
                    await event_emitter.emit(
                        "chat_response_searching",
                        data={"request_id": payload.request_id}
                    )
                    yield event_emitter.format_as_sse({
                        "event": "chat_response_searching",
                        "data": {"request_id": payload.request_id}
                    })

                    # Generate queries
                    api_start = time.time()
                    response = requests.post(
                        f"{base_url}/swagger/generate_queries",
                        json={"query": message.content},
                        timeout=30000,
                    )
                    api_time = time.time() - api_start

                    log_api_call_stats(
                        f"{base_url}/swagger/generate_queries",
                        "POST",
                        response.status_code,
                        api_time,
                        len(response.content) if response.content else 0,
                    )

                    if response.status_code == 200:
                        query_list = response.json()
                        LOGGER.info(
                            f"Generated {len(query_list)} swagger queries: {query_list}"
                        )

                        search_results = []
                        search_start = time.time()

                        for query_idx, query in enumerate(query_list):
                            LOGGER.debug(
                                f"Processing swagger query {query_idx+1}/{len(query_list)}: {query}"
                            )
                            query_search_start = time.time()

                            search_result = await _perform_swagger_search(
                                query=query, index_name=context.kbid or ""
                            )
                            query_search_time = time.time() - query_search_start

                            LOGGER.debug(
                                f"Query {query_idx+1} returned {len(search_result)} results in {query_search_time:.2f}s"
                            )
                            search_results.extend(search_result)

                        search_time = time.time() - search_start
                        LOGGER.info(
                            f"Swagger search completed - Total results: {len(search_results)} in {search_time:.2f}s"
                        )

                        final_search_results_map = {}
                        for endpoint in search_results:
                            final_search_results_map[endpoint["path"]] = endpoint
                        final_search_results = list(final_search_results_map.values())
                        final_search_results.sort(key=lambda x: x["path"])

                        context.content = json.dumps(final_search_results)
                        with open("swagger_search_results.json", "w") as f:
                            json.dump(final_search_results, f, indent=2)

                        search_references = SearchReferences(payload.request_id)
                        for endpoint in final_search_results:
                            search_references.add_search_result(
                                path=endpoint["path"],
                                type="file",
                                content=json.dumps(endpoint, indent=2),
                                name=endpoint["path"],
                            )
                        print("Swagger search references added to search_references")

                        # Emit search references
                        await event_emitter.emit(
                            "chat_response_references",
                            data=search_references.get_search_result()
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": search_references.get_search_result()
                        })
                        await asyncio.sleep(0.001)
                        LOGGER.debug("Emitted swagger search references to client")
                    else:
                        LOGGER.error(
                            f"Swagger query generation failed with status {response.status_code}"
                        )

                except Exception as e:
                    LOGGER.error(f"Error processing swagger context: {e}")
                    LOGGER.error(
                        f"Swagger context error traceback: {traceback.format_exc()}"
                    )

            context_time = time.time() - context_start
            LOGGER.debug(f"Context {context.type} processed in {context_time:.2f}s")

    context_processing_time = time.time() - context_processing_start
    LOGGER.info(f"Context processing completed in {context_processing_time:.2f}s")
    log_memory_usage("after_context_processing")

    final_payload = payload.model_dump(exclude_none=True)
    LOGGER.debug(f"Final payload size: {len(str(final_payload))} characters")

    try:
        assistant_response = ""

        # Initialize local LiteLLM tool-calling system
        LOGGER.info("Starting local LiteLLM chat stream with tool calling")
        request_start = time.time()

        # Get LiteLLM router
        router = get_litellm_session_router(payload.session__)
        if not router:
            raise ValueError("Failed to create LiteLLM router")

        # Initialize tool call handler
        tool_handler = ToolCallHandler(event_emitter, payload.session__, payload.request_id)

        # Build conversation messages from payload
        conversation_messages = []
        for message in payload.messages:
            if message.role == "user":
                conversation_messages.append({
                    "role": "user",
                    "content": message.content
                })
            elif message.role == "assistant":
                conversation_messages.append({
                    "role": "assistant",
                    "content": message.content
                })

        # Iterative conversation loop with tool calling
        max_iterations = 10
        iteration = 0
        chat_id = str(uuid4())

        LOGGER.info(f"Starting iterative conversation with chat ID: {chat_id}")

        while iteration < max_iterations:
            iteration += 1
            LOGGER.info(f"Conversation iteration {iteration}/{max_iterations}")

            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, breaking iteration loop")
                break

            # Make LLM completion request with tool calling
            try:
                llm_response = router.completion(
                    model="gpt-4o-mini",  # Use default model, can be made configurable
                    messages=conversation_messages,
                    tools=tool_handler.get_tool_schemas(),
                    tool_choice="auto",
                    stream=True,
                    temperature=0.1
                )

                # Process streaming response
                current_content = ""
                tool_calls = {}
                chunk_count = 0

                for chunk in llm_response:
                    if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                        LOGGER.info(f"Request {payload.request_id} cancelled, breaking chunk loop")
                        break

                    chunk_count += 1

                    # Handle tool calls
                    if hasattr(chunk.choices[0].delta, 'tool_calls') and chunk.choices[0].delta.tool_calls:
                        for tool_call in chunk.choices[0].delta.tool_calls:
                            tool_call_index = tool_call.index

                            if tool_call_index not in tool_calls:
                                tool_calls[tool_call_index] = {"name": "", "args": "", "id": ""}

                            if tool_call.id:
                                tool_calls[tool_call_index]["id"] = tool_call.id

                            if tool_call.function and tool_call.function.name:
                                tool_calls[tool_call_index]["name"] += tool_call.function.name

                            if tool_call.function and tool_call.function.arguments:
                                tool_calls[tool_call_index]["args"] += tool_call.function.arguments

                    # Handle content streaming
                    elif hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        current_content += content

                        # Stream content to client
                        response_data = {
                            "type": "content",
                            "request_id": payload.request_id,
                            "conversation_id": chat_id,
                            "data": {
                                "type": "content",
                                "memory_id": chat_id,
                                "chunk_index": chunk_count,
                                "content": content,
                            },
                        }

                        await event_emitter.emit("chat_response", data=response_data)
                        yield event_emitter.format_as_sse({
                            "event": "chat_response",
                            "data": response_data
                        })

                # If there's content and no tool calls, we're done
                if current_content.strip() and not tool_calls:
                    assistant_response += current_content
                    LOGGER.info(f"Conversation completed after {iteration} iterations")
                    break


                # Process tool calls if any
                if tool_calls:
                    LOGGER.info(f"Processing {len(tool_calls)} tool calls")

                    # Add assistant message with tool calls to conversation
                    assistant_message = {
                        "role": "assistant",
                        "content": current_content if current_content.strip() else None,
                        "tool_calls": []
                    }

                    # Prepare tool call results
                    tool_results = []
                    requires_followup = False

                    for tool_call_index, func_data in tool_calls.items():
                        if func_data["name"] and func_data["args"]:
                            try:
                                # Add to assistant message
                                assistant_message["tool_calls"].append({
                                    "id": func_data["id"] or f"call_{tool_call_index}",
                                    "type": "function",
                                    "function": {
                                        "name": func_data["name"],
                                        "arguments": func_data["args"]
                                    }
                                })

                                # Execute the tool call
                                tool_call_data = {
                                    "id": func_data["id"] or f"call_{tool_call_index}",
                                    "function": {
                                        "name": func_data["name"],
                                        "arguments": func_data["args"]
                                    }
                                }

                                tool_result, needs_followup = await tool_handler.execute_tool_call(tool_call_data)

                                # Add tool result to conversation
                                tool_results.append({
                                    "role": "tool",
                                    "tool_call_id": tool_result["tool_call_id"],
                                    "content": tool_result["content"]
                                })

                                if needs_followup:
                                    requires_followup = True

                            except json.JSONDecodeError as e:
                                LOGGER.error(f"JSON decode error for tool call {tool_call_index}: {e}")
                                tool_results.append({
                                    "role": "tool",
                                    "tool_call_id": func_data["id"] or f"call_{tool_call_index}",
                                    "content": json.dumps({
                                        "error": f"Invalid arguments: {e}",
                                        "status": "error"
                                    })
                                })
                                requires_followup = True
                            except Exception as e:
                                LOGGER.error(f"Error executing tool call {tool_call_index}: {e}")
                                tool_results.append({
                                    "role": "tool",
                                    "tool_call_id": func_data["id"] or f"call_{tool_call_index}",
                                    "content": json.dumps({
                                        "error": str(e),
                                        "status": "error"
                                    })
                                })
                                requires_followup = True

                    # Add assistant message and tool results to conversation
                    conversation_messages.append(assistant_message)
                    conversation_messages.extend(tool_results)

                    # Emit search references if available
                    if tool_handler.search_references and tool_handler.search_references.get_search_result().get("results"):
                        await event_emitter.emit(
                            "chat_response_references",
                            data=tool_handler.search_references.get_search_result()
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": tool_handler.search_references.get_search_result()
                        })
                        await asyncio.sleep(0.001)

                    # If no functions need follow-up, break the loop
                    if not requires_followup:
                        LOGGER.info("No functions require follow-up, ending conversation")
                        break
                else:
                    # No tool calls, conversation is complete
                    LOGGER.info("No tool calls, conversation complete")
                    break

            except Exception as e:
                LOGGER.error(f"Error in LLM completion iteration {iteration}: {e}")
                LOGGER.error(f"LLM error traceback: {traceback.format_exc()}")
                # Continue to next iteration or break if critical error
                break

        if iteration >= max_iterations:
            LOGGER.warning(f"Maximum conversation iterations ({max_iterations}) reached")

        request_time = time.time() - request_start
        LOGGER.info(f"Local LiteLLM chat stream completed in {request_time:.2f}s")

        # Generate follow-ups using local LLM if requested
        if payload.provide_followups and assistant_response.strip():
            LOGGER.info("Starting local follow-up generation")
            last_user_message_content = ""
            for message in reversed(payload.messages):
                if message.role == "user":
                    last_user_message_content = message.content
                    break

            LOGGER.debug(
                f"Last user message for follow-ups: {str(last_user_message_content)[:100]}..."
            )

            try:
                followup_start = time.time()

                # Use local LLM for follow-up generation
                followup_messages = [
                    {
                        "role": "system",
                        "content": "Generate 3-5 relevant follow-up questions based on the conversation. Return only a JSON array of strings, no other text."
                    },
                    {"role": "user", "content": last_user_message_content},
                    {"role": "assistant", "content": assistant_response},
                    {
                        "role": "user",
                        "content": "Generate follow-up questions for this conversation."
                    }
                ]

                followup_response = router.completion(
                    model="gpt-4o-mini",
                    messages=followup_messages,
                    temperature=0.7,
                    max_tokens=200
                )

                followup_time = time.time() - followup_start

                # Parse follow-up response
                followup_content = followup_response.choices[0].message.content
                try:
                    follow_ups = json.loads(followup_content)
                    if not isinstance(follow_ups, list):
                        follow_ups = []
                except json.JSONDecodeError:
                    LOGGER.warning("Failed to parse follow-up JSON, using fallback")
                    follow_ups = []

                LOGGER.info(
                    f"Generated {len(follow_ups)} follow-up suggestions in {followup_time:.2f}s"
                )

                await event_emitter.emit(
                    "chat_response_follow_ups",
                    data={
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    }
                )
                yield event_emitter.format_as_sse({
                    "event": "chat_response_follow_ups",
                    "data": {
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    }
                })
                LOGGER.debug("Follow-ups emitted to client")

            except Exception as e:
                LOGGER.error(f"Error during follow-ups generation: {e}")
                LOGGER.error(f"Follow-up error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("http_chat_request_complete")

        LOGGER.info(
            f"HTTP chat request completed successfully - Total time: {overall_time:.2f}s, "
            f"Response length: {len(assistant_response)} chars"
        )

    except Exception as e:
        overall_time = time.time() - overall_start_time
        LOGGER.error(f"Error during HTTP chat request after {overall_time:.2f}s: {e}")
        LOGGER.error(f"HTTP chat request error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)
        await event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        })
        raise


@route("POST", "/chat/stream")
async def chat_stream(request: Request):
    """
    HTTP streaming endpoint for chat functionality.
    Replaces WebSocket chat events with Server-Sent Events (SSE).
    """
    start_time = time.time()
    log_memory_usage("http_chat_start")
    
    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session", "http-session")

        LOGGER.info(f"HTTP chat stream request - Session: {session_id}")

        # Update session activity
        update_session_activity(session_id)

        # Validate payload
        validation_start = time.time()
        payload = ChatStreamRequest.model_validate(data)

        # Set session__ field to the session_id for external server requests
        if payload.session__ is None:
            payload.session__ = session_id

        validation_time = time.time() - validation_start
        
        LOGGER.info(
            f"Chat payload validated - Mode: {payload.mode}, "
            f"Request ID: {payload.request_id}, "
            f"Messages: {len(payload.messages)}, "
            f"Validation time: {validation_time:.3f}s"
        )
        
        # Log payload statistics
        total_contexts = sum(len(msg.context) for msg in payload.messages)
        context_types = {}
        for msg in payload.messages:
            for ctx in msg.context:
                context_types[ctx.type] = context_types.get(ctx.type, 0) + 1
        
        LOGGER.debug(f"Context distribution: {context_types}")
        LOGGER.debug(f"Total contexts: {total_contexts}")
        
        # Create event emitter for this request
        event_emitter = HTTPStreamEventEmitter(payload.request_id)
        
        # Route based on mode and create streaming generator
        async def generate_chat_stream() -> AsyncGenerator[str, None]:
            try:
                mode_processing_start = time.time()
                
                match payload.mode:
                    case "NORMAL":
                        LOGGER.info("Processing in NORMAL mode (server-based)")
                        async for chunk in process_chat_request_from_server_http(event_emitter, payload):
                            yield chunk
                    
                    case "PRO":
                        LOGGER.info("Processing in PRO mode (server-based)")
                        async for chunk in process_chat_request_from_server_http(event_emitter, payload):
                            yield chunk
                    
                    case "ECO":
                        LOGGER.info("Processing in ECO mode (local)")
                        dependency_start = time.time()
                        ensure_eco_mode_dependencies()
                        dependency_time = time.time() - dependency_start
                        LOGGER.debug(f"ECO mode dependencies ensured in {dependency_time:.2f}s")
                        
                        async for chunk in process_local_chat_request_http(event_emitter, payload):
                            yield chunk
                    
                    case _:
                        LOGGER.error(f"Invalid chat mode: {payload.mode}")
                        error_event = {
                            "event": "chat_response_error",
                            "data": {
                                "request_id": payload.request_id,
                                "error": f"Invalid mode: {payload.mode}"
                            }
                        }
                        yield f"event: chat_response_error\ndata: {json.dumps(error_event['data'])}\n\n"
                        return
                
                mode_processing_time = time.time() - mode_processing_start
                total_time = time.time() - start_time
                final_memory = log_memory_usage("http_chat_complete")
                
                # Update session activity again after successful completion
                update_session_activity(session_id)
                
                LOGGER.info(
                    f"HTTP chat stream completed successfully - Mode: {payload.mode}, "
                    f"Mode processing: {mode_processing_time:.2f}s, "
                    f"Total time: {total_time:.2f}s"
                )
                
            except Exception as e:
                total_time = time.time() - start_time
                LOGGER.error(f"Error in HTTP chat stream after {total_time:.2f}s: {e}")
                LOGGER.error(f"HTTP chat error traceback: {traceback.format_exc()}")
                log_memory_usage("http_chat_error")
                
                # Send error event
                error_event = {
                    "event": "chat_response_error",
                    "data": {
                        "request_id": payload.request_id,
                        "error": "An error occurred while processing the chat request."
                    }
                }
                yield f"event: chat_response_error\ndata: {json.dumps(error_event['data'])}\n\n"
        
        # Return streaming response
        return StreamingResponse(
            generate_chat_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error setting up HTTP chat stream after {total_time:.2f}s: {e}")
        LOGGER.error(f"HTTP chat setup error traceback: {traceback.format_exc()}")
        
        # Return error response
        error_response = {
            "error": "Failed to process chat request",
            "details": str(e)
        }
        
        async def error_stream():
            yield f"event: chat_response_error\ndata: {json.dumps(error_response)}\n\n"
        
        return StreamingResponse(
            error_stream(),
            media_type="text/plain",
            status_code=500
        )
