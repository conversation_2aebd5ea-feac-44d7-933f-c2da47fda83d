import platform


class PlatformDetector:
    """
    Detects the platform of the machine.
    """

    @staticmethod
    def is_amd86() -> bool:
        """
        Checks if the machine is an AMD x86 machine.
        """
        return "i386" in platform.processor().lower()

    @staticmethod
    def is_amd64() -> bool:
        """
        Checks if the machine is an AMD x86_64 machine.
        """
        return (
            "x86_64" in platform.processor().lower()
            or "amd64" in platform.processor().lower()
        )

    @staticmethod
    def is_aarch64() -> bool:
        """
        Checks if the machine is an ARM aarch64 machine.
        """
        return "aarch64" in platform.processor().lower()

    @staticmethod
    def is_arm86() -> bool:
        """
        Checks if the machine is an ARM arm86 machine.
        """
        return "arm" in platform.processor().lower() and not PlatformDetector.is_arm64()

    @staticmethod
    def is_arm64() -> bool:
        """
        Checks if the machine is an ARM arm64 machine.
        """
        return (
            "arm" in platform.processor().lower() and "64" in platform.machine().lower()
        )

    @staticmethod
    def is_arm() -> bool:
        """
        Checks if the machine is an ARM machine.
        """
        return "arm" in platform.processor().lower()

    @staticmethod
    def is_amd() -> bool:
        """
        Checks if the machine is an AMD machine.
        """
        return (
            "x86" in platform.processor().lower()
            or "amd" in platform.processor().lower()
        )

    @staticmethod
    def get_arch_name() -> str:
        """
        Gets the architecture name of the machine.
        """
        return platform.processor().lower()

    @staticmethod
    def is_windows() -> bool:
        """
        Checks if the operating system is Windows.
        """
        return platform.system().lower() == "windows"

    @staticmethod
    def is_linux() -> bool:
        """
        Checks if the operating system is Linux.
        """
        return platform.system().lower() == "linux"

    @staticmethod
    def is_darwin() -> bool:
        """
        Checks if the operating system is macOS (Darwin).
        """
        return platform.system().lower() == "darwin"

    @staticmethod
    def is_macos() -> bool:
        """
        Alias for is_darwin() - checks if the operating system is macOS.
        """
        return PlatformDetector.is_darwin()

    @staticmethod
    def get_os_name() -> str:
        """
        Gets the name of the operating system.
        """
        return platform.system().lower()

    @staticmethod
    def is_snapdragon_arm() -> bool:
        """
        Checks if the machine is a Snapdragon ARM machine.
        """
        is_snapdragon = "qualcomm" in platform.processor().lower()
        return PlatformDetector.is_arm() and is_snapdragon
