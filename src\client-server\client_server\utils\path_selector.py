import os
import sys
import json

from pathlib import Path


class HomePathFinder:
    def __init__(self):
        pass

    def get_home_path(self) -> Path:
        """Get the user's home directory path with multiple fallback methods"""
        # Try multiple methods to get the home directory
        methods = [
            # Method 1: pathlib's Path.home() (standard way)
            lambda: Path.home(),
            # Method 2: expanduser
            lambda: Path(os.path.expanduser("~")),
            # Method 3: environment variables
            lambda: Path(os.environ.get("USERPROFILE") or os.environ.get("HOME", "")),
            # Method 4: Windows specific - try to get from registry if on Windows
            lambda: self._get_windows_home() if sys.platform == "win32" else Path(),
            # Method 5: Last resort - use current working directory
            lambda: Path(os.getcwd()),
        ]

        for method in methods:
            try:
                path = method()
                if path and path.exists() and path.is_dir():
                    return path
            except Exception:
                continue

        # If all methods fail, return current directory as last resort
        return Path(os.getcwd())

    def _get_windows_home(self) -> Path:
        """Get Windows home directory from registry as fallback"""
        try:
            import winreg

            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders",
            ) as key:
                home = winreg.QueryValueEx(key, "Personal")[0]
                return Path(
                    home
                ).parent  # Get parent of Documents folder which should be user home
        except Exception:
            return Path()


class PathSelector:
    """
    Static class to handle base path operations using settings.json file.
    All operations read/write directly from/to the settings file.
    """

    @staticmethod
    def get_user_home_path() -> Path:
        """Get the user's home directory path"""
        return HomePathFinder().get_home_path()

    @staticmethod
    def get_settings_file() -> Path:
        """Get the path to the settings file"""
        return PathSelector.get_user_home_path() / "settings.json"

    @staticmethod
    def _load_settings() -> dict:
        """Load settings from ~/settings.json"""
        settings_file = PathSelector.get_settings_file()
        try:
            if settings_file.exists():
                with open(settings_file, "r") as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"Error loading settings: {e}")
            return {}

    @staticmethod
    def _save_settings(settings: dict) -> None:
        """Save settings to ~/settings.json"""
        try:
            with open(PathSelector.get_settings_file(), "w") as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Error saving settings: {e}")

    @staticmethod
    def set_base_path(base_path: Path) -> None:
        """Set the base path in settings"""
        settings = PathSelector._load_settings()
        settings["base_path"] = str(base_path)
        PathSelector._save_settings(settings)

    @staticmethod
    def get_base_path() -> Path:
        """Get the base path from settings, defaulting to user home if not set"""
        settings = PathSelector._load_settings()
        final_path = PathSelector.get_user_home_path() / ".codemate"
        if "base_path" in settings:
            final_path = Path(settings["base_path"])

        os.makedirs(final_path, exist_ok=True)
        return final_path

    @staticmethod
    def get_cache_path(make_if_not_exists: bool = True):
        path = PathSelector.get_base_path() / ".cache"
        if make_if_not_exists:
            os.makedirs(path, exist_ok=True)
        return path

    @staticmethod
    def get_logs_path(make_if_not_exists: bool = True):
        path = PathSelector.get_base_path() / ".logs"
        if make_if_not_exists:
            os.makedirs(path, exist_ok=True)
        return path

    @staticmethod
    def get_qdrant_db_path(make_if_not_exists: bool = True):
        path = PathSelector.get_base_path() / ".neocortex"
        if make_if_not_exists:
            os.makedirs(path, exist_ok=True)
        return path
