import psutil
import platform
import subprocess
import re


class SystemResource:
    @staticmethod
    def get_ram_size_in_gb():
        """Get the total RAM size in gigabytes"""
        memory = psutil.virtual_memory()
        return round(memory.total / (1024**3), 2)  # Convert bytes to GB

    @staticmethod
    def get_cpu_count():
        """Get the number of CPU cores"""
        return psutil.cpu_count()

    @staticmethod
    def get_gpu_count():
        """Get the number of GPUs available on the system"""
        try:
            # Try to use nvidia-smi if available
            if platform.system() == "Windows":
                output = subprocess.check_output("nvidia-smi", shell=True)
            else:
                output = subprocess.check_output(["nvidia-smi"])
            # Count the number of GPUs by looking for "| 0" pattern
            gpu_count = len(re.findall(r"\|\s+0", output.decode()))
            return gpu_count
        except (subprocess.SubprocessError, FileNotFoundError):
            # If nvidia-smi is not available, try alternative methods
            try:
                # For macOS
                if platform.system() == "Darwin":
                    output = subprocess.check_output(
                        ["system_profiler", "SPDisplaysDataType"]
                    )
                    # Count the number of GPUs
                    gpu_count = len(re.findall(r"Chipset Model:", output.decode()))
                    return gpu_count
                # For Linux
                elif platform.system() == "Linux":
                    # Try to read from /proc/driver/nvidia/gpus
                    try:
                        with open("/proc/driver/nvidia/gpus", "r") as f:
                            return len(f.readlines())
                    except FileNotFoundError:
                        # Try to use lspci to count NVIDIA devices
                        output = subprocess.check_output(["lspci", "-d", "10de:"])
                        return len(output.decode().split("\n")) - 1
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
            return 0  # Return 0 if no GPU detection method works

    @staticmethod
    def get_vram_size_in_gb():
        """Get the total VRAM size in gigabytes for NVIDIA GPUs"""
        try:
            # Try to use nvidia-smi if available
            if platform.system() == "Windows":
                output = subprocess.check_output(
                    "nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits",
                    shell=True,
                )
            else:
                output = subprocess.check_output(
                    [
                        "nvidia-smi",
                        "--query-gpu=memory.total",
                        "--format=csv,noheader,nounits",
                    ]
                )

            # Parse the output to get VRAM size in MB and convert to GB
            vram_sizes = [
                int(size.strip())
                for size in output.decode().split("\n")
                if size.strip()
            ]
            if vram_sizes:
                # Sum up all GPU VRAM sizes and convert to GB
                total_vram_gb = sum(vram_sizes) / 1024
                return round(total_vram_gb, 2)
            return 0
        except (subprocess.SubprocessError, FileNotFoundError):
            # If nvidia-smi is not available, try alternative methods
            try:
                # For macOS
                if platform.system() == "Darwin":
                    output = subprocess.check_output(
                        ["system_profiler", "SPDisplaysDataType"]
                    )
                    # Try to extract VRAM information if available
                    vram_info = re.findall(r"VRAM \(Total\): (\d+) MB", output.decode())
                    if vram_info:
                        return round(int(vram_info[0]) / 1024, 2)
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
            return 0  # Return 0 if no VRAM detection method works
