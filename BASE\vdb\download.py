#!/usr/bin/env python3
"""
Qdrant Binary Downloader
Automatically detects platform and downloads the appropriate Qdrant binary.
"""

import os
import sys
import hashlib
import requests
import zipfile
import tarfile
import shutil
from pathlib import Path
from typing import Optional, Tuple
from BASE.utils.platform_detector import PlatformDetector
from BASE.utils.path_selector import PathSelector
from logger.log import logger


class QdrantDownloader:
    BASE_URL = "https://github.com/qdrant/qdrant/releases/download/v1.15.0"
    
    # Binary mappings with their expected SHA256 hashes
    BINARIES = {
        "aarch64-apple-darwin": {
            "filename": "qdrant-aarch64-apple-darwin.tar.gz",
            "sha256": "d8d7fe7f03aed4a0e7b94673d0d09e9d17b7aeb12545cb519e1f0a74a83bcb91",
            "size": "27 MB"
        },
        "x86_64-apple-darwin": {
            "filename": "qdrant-x86_64-apple-darwin.tar.gz", 
            "sha256": "a1e342a0f2ed0c15bee57730d8ca417feeb1eb224447a2371d039251d1eb65e7",
            "size": "28.9 MB"
        },
        "x86_64-pc-windows-msvc": {
            "filename": "qdrant-x86_64-pc-windows-msvc.zip",
            "sha256": "ba7379576461929b71df5fd0569986e8361915a7314dfa74ec342dd5cea65624", 
            "size": "27.3 MB"
        },
        "x86_64-unknown-linux-gnu": {
            "filename": "qdrant-x86_64-unknown-linux-gnu.tar.gz",
            "sha256": "83d729539e50d501efe55de365495ac7c60efc559a35aa4184b5301b0a32b1df",
            "size": "30.4 MB"
        },
        "x86_64-appimage": {
            "filename": "qdrant-x86_64.AppImage",
            "sha256": None,  # No hash provided in the original data
            "size": "Unknown"
        }
    }

    def __init__(self, download_dir: str = PathSelector.get_base_path()):
        """Initialize downloader with target directory."""
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)

    def detect_platform(self) -> Optional[str]:
        """Detect the current platform and return the appropriate binary key."""
        
        logger.info("Detecting platform...")
        logger.info(f"OS: {PlatformDetector.get_os_name()}")
        logger.info(f"Architecture: {PlatformDetector.get_arch_name()}")
        
        # macOS (Darwin)
        if PlatformDetector.is_macos():
            if PlatformDetector.is_aarch64() or PlatformDetector.is_arm64():
                return "aarch64-apple-darwin"
            elif PlatformDetector.is_amd64():
                return "x86_64-apple-darwin"
        
        # Windows
        elif PlatformDetector.is_windows():
            if PlatformDetector.is_amd64() or PlatformDetector.is_amd64():
                return "x86_64-pc-windows-msvc"
        
        # Linux
        elif PlatformDetector.is_linux():
            if PlatformDetector.is_amd64():
                return "x86_64-unknown-linux-gnu"
        
        return None

    def calculate_sha256(self, filepath: str) -> str:
        """Calculate SHA256 hash of a file."""
        hash_sha256 = hashlib.sha256()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()

    def verify_download(self, filepath: str, expected_hash: Optional[str]) -> bool:
        """Verify downloaded file integrity using SHA256."""
        if not expected_hash:
            logger.warning("⚠️  No hash provided for verification, skipping integrity check")
            return True
            
        logger.info("Verifying file integrity...")
        actual_hash = self.calculate_sha256(filepath)
        
        if actual_hash == expected_hash:
            logger.info("✅ File integrity verified")
            return True
        else:
            logger.error(f"Hash mismatch!")
            logger.info(f"Expected: {expected_hash}")
            logger.info(f"Actual:   {actual_hash}")
            return False

    def download_file(self, url: str, filepath: str) -> bool:
        """Download file with progress indication."""
        try:
            logger.info(f"Downloading from: {url}")
            logger.info(f"Saving to: {filepath}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            logger.info(f"\rProgress: {progress:.1f}% ({downloaded:,}/{total_size:,} bytes)", end='')
            
            return True
            
        except requests.RequestException as e:
            logger.error(f"Download failed: {e}")
            return False

    def extract_archive(self, archive_path: Path, platform_key: str) -> bool:
        """Extract downloaded archive and rename executable."""
        try:
            logger.info("Extracting archive...")
            
            # Determine extraction method based on file type
            if archive_path.suffix == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(self.download_dir)
            elif archive_path.suffix == '.gz' and archive_path.name.endswith('.tar.gz'):
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(self.download_dir)
            else:
                logger.error(f"Unsupported archive format: {archive_path}")
                return False
            
            logger.info("Archive extracted successfully")
            
            # Find the executable and rename it
            return self._rename_executable(platform_key)
            
        except Exception as e:
            logger.error(f"Failed to extract archive: {e}")
            return False

    def _rename_executable(self, platform_key: str) -> bool:
        """Find and rename the Qdrant executable to a standard name."""
        try:
            # Determine target executable name based on platform
            if PlatformDetector.is_windows():
                target_name = "qdrant.exe"
            else:
                target_name = "qdrant"
            
            target_path = self.download_dir / target_name
            
            # If target already exists and is valid, skip
            if target_path.exists():
                logger.info(f"Executable already exists: {target_path}")
                return True
            
            # Search for the Qdrant executable in the download directory
            executable_patterns = ["qdrant", "qdrant.exe"]
            found_executable = None
            
            for item in self.download_dir.rglob("*"):
                if item.is_file() and item.name.lower() in [p.lower() for p in executable_patterns]:
                    # Skip if it's already the target file
                    if item.name == target_name and item.parent == self.download_dir:
                        logger.info(f"Executable already in correct location: {item}")
                        return True
                    found_executable = item
                    break
            
            if not found_executable:
                logger.error("Could not find Qdrant executable in extracted files")
                # List contents for debugging
                logger.info("Contents of download directory:")
                for item in self.download_dir.rglob("*"):
                    logger.info(f"  {item}")
                return False
            
            # Move and rename the executable
            logger.info(f"Moving {found_executable} to {target_path}")
            shutil.move(str(found_executable), str(target_path))
            
            # Make executable on Unix-like systems
            if not PlatformDetector.is_windows():
                os.chmod(str(target_path), 0o755)
                logger.info("Made executable")
            
            # Clean up any remaining extracted directories
            self._cleanup_extracted_dirs()
            
            logger.info(f"✅ Executable ready at: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to rename executable: {e}")
            return False

    def _cleanup_extracted_dirs(self):
        """Remove any extracted directories, keeping only the executable."""
        try:
            for item in self.download_dir.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    logger.info(f"Cleaning up extracted directory: {item}")
                    shutil.rmtree(item)
        except Exception as e:
            logger.warning(f"Failed to clean up some extracted directories: {e}")

    def download_qdrant(self, prefer_appimage: bool = False) -> bool:
        """Main method to download Qdrant binary for current platform."""
        
        platform_key = self.detect_platform()
        
        if not platform_key:
            logger.error("Unsupported platform detected!")
            logger.info("Supported platforms:")
            for key in self.BINARIES.keys():
                if key != "x86_64-appimage":
                    logger.info(f"  - {key}")
            return False

        # For Linux x86_64, offer AppImage option
        if platform_key == "x86_64-unknown-linux-gnu" and prefer_appimage:
            platform_key = "x86_64-appimage"
            logger.info("Using AppImage version for Linux")

        binary_info = self.BINARIES[platform_key]
        filename = binary_info["filename"]
        expected_hash = binary_info["sha256"]
        
        logger.info(f"Selected binary: {filename}")
        logger.info(f"📏 Size: {binary_info['size']}")
        
        # Check if final executable already exists
        if PlatformDetector.is_windows():
            final_executable = self.download_dir / "qdrant.exe"
        else:
            final_executable = self.download_dir / "qdrant"
        
        if final_executable.exists():
            logger.info(f"Qdrant executable already exists: {final_executable}")
            return True
        
        # Construct download URL and local path
        download_url = f"{self.BASE_URL}/{filename}"
        local_path = self.download_dir / filename
        
        # Check if archive already exists and is valid
        download_needed = True
        if local_path.exists():
            logger.info(f"Archive already exists: {local_path}")
            if expected_hash and self.verify_download(str(local_path), expected_hash):
                logger.info("Existing archive is valid!")
                download_needed = False
            else:
                logger.info("Re-downloading due to integrity issues...")
                local_path.unlink()
        
        # Download the file if needed
        if download_needed:
            if not self.download_file(download_url, str(local_path)):
                return False
            
            # Verify download
            if not self.verify_download(str(local_path), expected_hash):
                logger.info("Removing corrupted download...")
                local_path.unlink()
                return False
        
        # Handle AppImage (no extraction needed)
        if filename.endswith('.AppImage'):
            target_path = self.download_dir / "qdrant"
            shutil.move(str(local_path), str(target_path))
            os.chmod(str(target_path), 0o755)
            logger.info("AppImage ready and executable")
            return True
        
        # Extract archive and rename executable
        if not self.extract_archive(local_path, platform_key):
            return False
        
        # Remove the downloaded archive
        try:
            logger.info("Cleaning up downloaded archive...")
            local_path.unlink()
            logger.info("Archive removed")
        except Exception as e:
            logger.warning(f"Failed to remove archive: {e}")
        
        logger.info("✅ Qdrant binary setup completed successfully!")
        return True


def main():
    try:
        downloader = QdrantDownloader()
        success = downloader.download_qdrant()
        # create the config file for qdrant:
        config = """# qdrant.yaml

service:
  host: 127.0.0.1
  http_port: 45215
  grpc_port: 45216

storage:
  storage_path: ./.vdb
  snapshots_path: ./.vdb/snapshots
  wal_path: ./.vdb/wal
"""
        with open(PathSelector.get_base_path() / "qdrant_config.yaml", "w") as e:
            e.write(config)
            e.close()
        
        if success:
            logger.info("Download and setup completed successfully!")
            return True
        else:
            logger.info("Download failed!")
            return False
            
    except KeyboardInterrupt:
        logger.info("Download cancelled by user")
        return False
    except Exception as e:
        logger.info(f"Unexpected error: {e}")
        return False


# if __name__ == "__main__":
#     main()