"""
Tool call handler for processing LLM tool calls.

This module contains the ToolCallHandler class that processes tool calls
from LLM responses and routes them to appropriate action handlers.
"""

import json
from typing import Any

from client_server.utils.actions.swagger_search import process_swagger_search
from client_server.utils.actions.context_search import process_context_search
from client_server.utils.actions.folder_search import process_folder_search
from client_server.utils.actions.web_search import process_web_search
from client_server.utils.actions.utils import SearchReferences
from client_server.core.logger import LOGGER

from .schemas import get_tool_schemas


class ToolCallHandler:
    """
    Handles tool calls from LLM responses and routes them to appropriate action handlers.
    Integrates with the existing action system while providing LLM tool calling interface.
    """

    def __init__(self, event_emitter, session_id: str, request_id: str, web_search_enabled: bool = False, context_types: set[str] = None):
        self.event_emitter = event_emitter
        self.session_id = session_id
        self.request_id = request_id
        self.web_search_enabled = web_search_enabled
        self.context_types = context_types or set()
        self.search_references = SearchReferences(request_id)

    def get_tool_schemas(self) -> list[dict[str, Any]]:
        """
        Get tool schemas that the LLM can understand and call.
        Conditionally includes tools based on context types and web search settings.
        """
        return get_tool_schemas(
            context_types=self.context_types,
            web_search_enabled=self.web_search_enabled
        )

    @staticmethod
    def extract_context_types(payload) -> set[str]:
        """
        Extract context types from the payload messages.

        Args:
            payload: The chat request payload containing messages with context

        Returns:
            Set of context types found in the payload
        """
        context_types = set()

        for message in payload.messages:
            # Handle both dict and object message formats
            message_contexts = message.get("context", []) if isinstance(message, dict) else getattr(message, "context", [])

            for context in message_contexts:
                # Handle both dict and object context formats
                context_type = context.get("type") if isinstance(context, dict) else getattr(context, "type", None)
                if context_type:
                    context_types.add(context_type)

        return context_types

    async def execute_tool_call(self, tool_call: dict[str, Any]) -> tuple[dict[str, Any], bool]:
        """
        Execute a tool call and return the result.

        Args:
            tool_call: Tool call from LLM response

        Returns:
            Tuple of (tool_result, needs_followup)
        """
        function_name = tool_call["function"]["name"]
        function_args = json.loads(tool_call["function"]["arguments"])
        tool_id = tool_call["id"]

        LOGGER.info(f"Executing tool call: {function_name} with args: {function_args}")

        try:
            # Emit searching event before tool execution
            await self.event_emitter.emit(
                "chat_response_searching",
                data={"request_id": self.request_id, "action_id": tool_id}
            )

            # Route to appropriate action handler
            if function_name == "context_search":
                result, search_refs = await process_context_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    kbid=function_args["kbid"],
                    search_references=self.search_references
                )

            elif function_name == "folder_search":
                result, search_refs = await process_folder_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    folder_path=function_args["folder_path"],
                    index_name=function_args["kbid"],
                    search_references=self.search_references
                )

            elif function_name == "web_search":
                result, search_refs = await process_web_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    session=self.session_id,
                    search_references=self.search_references
                )

            elif function_name == "swagger_search":
                result, search_refs = await process_swagger_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    kbid=function_args["kbid"],
                    search_refrences=self.search_references
                )

            else:
                raise ValueError(f"Unknown tool function: {function_name}")

            # Update search references
            self.search_references = search_refs

            # Emit search references if available
            if search_refs and search_refs.get_search_result().get("results"):
                await self.event_emitter.emit(
                    "chat_response_references",
                    data=search_refs.get_search_result()
                )

            # Format tool result for LLM
            tool_result = {
                "tool_call_id": tool_id,
                "content": json.dumps(result)
            }

            # Determine if LLM follow-up is needed
            needs_followup = self._needs_llm_followup(function_name, function_args, result)

            return tool_result, needs_followup

        except Exception as e:
            LOGGER.error(f"Error executing tool call {function_name}: {e}")
            error_result = {
                "tool_call_id": tool_id,
                "content": json.dumps({
                    "error": str(e),
                    "status": "error"
                })
            }
            return error_result, True  # Error requires follow-up

    def _needs_llm_followup(self, function_name: str, args: dict[str, Any], result: Any) -> bool:
        """
        Determine if a tool call result needs LLM follow-up processing.
        """
        # Always need follow-up for tool calls to process and respond to results
        return True
