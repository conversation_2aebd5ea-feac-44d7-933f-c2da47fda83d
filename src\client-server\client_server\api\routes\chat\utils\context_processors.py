"""
Context processors for handling different types of context in chat requests.

This module provides specialized processors for handling file and swagger contexts,
extracting the inline logic from the main server handlers for better maintainability.
"""

import asyncio
import json
import time
import traceback
from typing import Any, Dict, Union

import requests

from client_server.utils.actions.swagger_search import _perform_swagger_search
from client_server.utils.actions.utils import SearchReferences
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_file_stats
from client_server.core.state import G_BASE_URL
from ..handlers import format_filepath
from ..streaming import HTTPStreamEventEmitter


class FileContextProcessor:
    """Processor for handling file context types."""
    
    @staticmethod
    def process_file_context(context: Union[Dict[str, Any], Any], context_name: str = "") -> None:
        """
        Process a file context by reading the file content and adding it to the context.
        
        Args:
            context: The context object (dict or object with attributes)
            context_name: Name of the context for logging purposes
        """
        context_path = context.get("path") if isinstance(context, dict) else getattr(context, "path", None)
        
        if not context_path:
            LOGGER.warning(f"File context without path: {context_name}")
            if isinstance(context, dict):
                context["content"] = ""
            else:
                context.content = ""
            return
        
        try:
            file_path = format_filepath(context_path)
            file_size = log_file_stats(file_path)
            
            read_start = time.time()
            with open(file_path, "r", encoding="utf-8") as file:
                file_content = file.read()
                if isinstance(context, dict):
                    context["content"] = file_content
                else:
                    context.content = file_content
            read_time = time.time() - read_start
            
            content_length = len(file_content)
            content_lines = file_content.count("\n") + 1
            LOGGER.debug(
                f"File read - Path: {file_path}, Size: {file_size} bytes, "
                f"Content: {content_length} chars, {content_lines} lines, "
                f"Read time: {read_time:.2f}s"
            )
            
        except Exception as e:
            LOGGER.error(f"Failed to read file {context_path}: {e}")
            if isinstance(context, dict):
                context["content"] = ""
            else:
                context.content = ""


class SwaggerContextProcessor:
    """Processor for handling swagger context types."""
    
    @staticmethod
    async def process_swagger_context(
        context: Union[Dict[str, Any], Any],
        message: Union[Dict[str, Any], Any],
        event_emitter: HTTPStreamEventEmitter,
        request_id: str
    ) -> None:
        """
        Process a swagger context by performing search operations and adding results to the context.
        
        Args:
            context: The context object (dict or object with attributes)
            message: The message object containing the content for search
            event_emitter: Event emitter for streaming responses
            request_id: Request ID for tracking
        """
        context_kbid = context.get("kbid") if isinstance(context, dict) else getattr(context, "kbid", "")
        LOGGER.info(f"Processing swagger context - KBID: {context_kbid}")
        
        base_url = G_BASE_URL.get().general
        
        try:
            # Emit searching event before swagger search
            await event_emitter.emit(
                "chat_response_searching",
                data={"request_id": request_id}
            )
            
            # Generate queries
            api_start = time.time()
            message_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")
            response = requests.post(
                f"{base_url}/swagger/generate_queries",
                json={"query": message_content},
                timeout=30000,
            )
            api_time = time.time() - api_start
            
            from client_server.core.logger.utils import log_api_call_stats
            log_api_call_stats(
                f"{base_url}/swagger/generate_queries",
                "POST",
                response.status_code,
                api_time,
                len(response.content) if response.content else 0,
            )
            
            if response.status_code == 200:
                query_list = response.json()
                LOGGER.info(f"Generated {len(query_list)} swagger queries: {query_list}")
                
                search_results = []
                search_start = time.time()
                
                for query_idx, query in enumerate(query_list):
                    LOGGER.debug(f"Processing swagger query {query_idx+1}/{len(query_list)}: {query}")
                    query_search_start = time.time()
                    
                    search_result = await _perform_swagger_search(
                        query=query, index_name=context_kbid or ""
                    )
                    query_search_time = time.time() - query_search_start
                    
                    LOGGER.debug(
                        f"Query {query_idx+1} returned {len(search_result)} results in {query_search_time:.2f}s"
                    )
                    search_results.extend(search_result)
                
                search_time = time.time() - search_start
                LOGGER.info(f"Swagger search completed - Total results: {len(search_results)} in {search_time:.2f}s")
                
                # Process and deduplicate results
                final_search_results_map = {}
                for endpoint in search_results:
                    final_search_results_map[endpoint["path"]] = endpoint
                final_search_results = list(final_search_results_map.values())
                final_search_results.sort(key=lambda x: x["path"])
                
                # Set context content
                if isinstance(context, dict):
                    context["content"] = json.dumps(final_search_results)
                else:
                    context.content = json.dumps(final_search_results)
                
                # Save results to file for debugging
                with open("swagger_search_results.json", "w") as f:
                    json.dump(final_search_results, f, indent=2)
                
                # Create and populate search references
                search_references = SearchReferences(request_id)
                for endpoint in final_search_results:
                    search_references.add_search_result(
                        path=endpoint["path"],
                        type="file",
                        content=json.dumps(endpoint, indent=2),
                        name=endpoint["path"],
                    )
                print("Swagger search references added to search_references")
                
                # Emit search references
                await event_emitter.emit(
                    "chat_response_references",
                    data=search_references.get_search_result()
                )
                await asyncio.sleep(0.001)
                LOGGER.debug("Emitted swagger search references to client")
                
                return search_references
                
            else:
                LOGGER.error(f"Swagger query generation failed with status {response.status_code}")
                
        except Exception as e:
            LOGGER.error(f"Error processing swagger context: {e}")
            LOGGER.error(f"Swagger context error traceback: {traceback.format_exc()}")
            
        return None
