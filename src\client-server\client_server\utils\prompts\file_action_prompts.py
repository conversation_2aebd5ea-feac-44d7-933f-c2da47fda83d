"""
File Action Prompts for AI-powered file analysis and automated improvements

This module contains message templates for different file action operations
including code review, documentation generation, security scanning, fix suggestions,
and automated fix application following the established CodeLens pattern.
"""

from typing import Dict, List, Any, Optional
from enum import Enum




class SafetyLevel(Enum):
    """Safety levels for automated fixes"""
    SAFE_ONLY = "safe_only"
    REVIEW_REQUIRED = "review_required"
    MANUAL_APPROVAL = "manual_approval"


class FileActionPrompts:
    """Message templates for file action operations"""
    
    @staticmethod
    def get_code_review_messages(
        language: str, 
        file_path: str,
        file_content: str, 
    ) -> List[Dict[str, str]]:
        """
        Generate messages for comprehensive code review
        
        Args:
            language: Programming language of the code
            file_path: Path to the file being reviewed
            file_content: Complete file content to review
        """
        
        system_prompt = f"""You are an expert code reviewer. Analyze the code and provide feedback focusing on:
- Code correctness and bugs
- Performance issues
- Security concerns
- Code style
- Maintainability
- Documentation
- Error handling

Provide analysis in this XML format:
<analysis>
    <score>[1-10]</score>
    <summary>[brief assessment]</summary>
    <issues>
        <issue>
            <severity>CRITICAL|HIGH|MEDIUM|LOW</severity>
            <category>[issue type]</category>
            <line>[number]</line>
            <description>[issue details]</description>
            <fix>[how to fix]</fix>
        </issue>
    </issues>
</analysis>"""

        user_prompt = f"""Review this {language} code:
```
{file_content}
```"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_documentation_generation_messages(
        language: str,
        file_path: str,
        file_content: str,
    ) -> List[Dict[str, str]]:
        """
        Generate messages for documentation generation/update
        
        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content
            
        Returns:
            List of message dictionaries for the LLM containing system and user prompts
        """
        system_prompt = f"""
You are "CodeMate-DocGen", a seasoned technical writer. You are expert in generating documentation for code in markdown format.

Here's the code in {language} for which you need to generate documentation in markdown format:
<code>
{file_content}
</code>

Expected response:
<docs>
</docs>
   """

        user_prompt = f"""File: {file_path}

Code to Document:
```{language}
{file_content}
```

Please generate comprehensive documentation following {language} best practices and the specified format."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    @staticmethod
    def get_security_scan_messages(
        language: str,
        file_path: str,
        file_content: str,
    ) -> List[Dict[str, str]]:
        """
        Generate messages for security vulnerability detection

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content
        """

        system_prompt = f"""
You are "CodeMate-SecAuditor", an elite cybersecurity expert and code auditor with decades of experience in finding vulnerabilities across all programming languages and frameworks.

Your mission: Perform a comprehensive security analysis of the provided {language} code. Look for vulnerabilities, security anti-patterns, potential attack vectors, and compliance issues.

Focus areas include but not limited to:
- Input validation and sanitization flaws
- Authentication and authorization weaknesses  
- SQL injection, XSS, CSRF vulnerabilities
- Insecure cryptographic implementations
- Information disclosure risks
- Buffer overflows and memory safety issues
- Race conditions and concurrency problems
- Dependency and supply chain risks
- Insecure configurations and hardcoded secrets
- Business logic flaws

Here's the {language} code to analyze:
<code>
{file_content}
</code>

Provide your analysis in this exact format:

<overall_eval>
[Provide a comprehensive overall security evaluation in markdown format. Include severity assessment, risk summary, and general recommendations. Be thorough but concise.]
</overall_eval>

<problems>
<problem>
<title>[Concise title of the security issue]</title>
<severity>[Rate the severity from 1-10, where 1 is lowest and 10 is highest risk]</severity>
<description>[Detailed technical description of the vulnerability, potential impact, attack scenarios, and specific remediation steps in markdown format]</description>
<target_code_block>[The exact code snippet where this issue exists - copy the problematic lines exactly as they appear]</target_code_block>
</problem>
<problem>
<title>[Next security issue title]</title>
<severity>[1-10 severity rating]</severity>
<description>[Next issue description]</description>
<target_code_block>[Next problematic code block]</target_code_block>
</problem>
</problems>

If no security issues are found, still provide the overall_eval but use empty problems tags: <problems></problems>
   """

        user_prompt = f"""File Path: {file_path}
Language: {language}

Code to Scan for Security Vulnerabilities:
```{language}
{file_content}
```

Please perform a comprehensive security analysis following the specified format. Rate each issue's severity on a scale of 1-10."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    @staticmethod
    def get_fix_suggestions_messages(
        language: str,
        file_path: str,
        file_content: str,
        title: str,
        description: str,
        target_code_block: str
    ) -> List[Dict[str, str]]:
        """
        Generate messages for automated fix suggestions

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content

        """


        system_prompt = f"""
You are "CodeMate-SecFixer", an elite security remediation specialist with decades of experience in fixing vulnerabilities across all programming languages and frameworks.

Your mission: Generate precise code fixes for the identified security vulnerability. Provide minimal, targeted changes that address the security issue without breaking existing functionality.

Security Issue Details:
- Title: {title}
- Description: {description}
- Language: {language}
- file_content: {file_content}

Original Code to Fix:
<target_code>
{target_code_block}
</target_code>

Guidelines for your fixes:
- Make MINIMAL changes - only fix what's necessary for security
- Preserve existing functionality and logic flow
- Follow secure coding best practices for {language}
- Use proper input validation, sanitization, and encoding
- Implement appropriate error handling
- Add security-focused comments where helpful
- Ensure fixes are production-ready and performant

Provide your fixes in this exact format:

<fixes>
<fix>
<old_code>[Exact code block that needs to be replaced - copy exactly from target_code]</old_code>
<new_code>[The secure replacement code block]</new_code>
</fix>
<fix>
<old_code>[Next code block to replace]</old_code>
<new_code>[Next secure replacement]</new_code>
</fix>
</fixes>

Important:
- Each <old_code> must match EXACTLY what exists in the target_code
- Provide multiple fixes if the vulnerability requires changes in multiple locations
- If the fix requires adding new imports/dependencies, include them in separate fix blocks
- Keep each fix focused and atomic
   """

        user_prompt = f"""File Path: {file_path}
Language: {language}

Current Code:
```{language}
{file_content}
```
Please generate specific, actionable fixes for these issues"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    @staticmethod
    def get_auto_apply_messages(
        language: str,
        file_path: str,
        file_content: str,
        fixes: List[Dict[str, Any]],
        safety_level: SafetyLevel = SafetyLevel.SAFE_ONLY
    ) -> List[Dict[str, str]]:
        """
        Generate messages for auto-applying fixes to code

        Args:
            language: Programming language of the code
            file_path: Path to the file
            file_content: Complete file content
            fixes: List of fixes to apply
            safety_level: Safety level for auto-application
        """
        safety_instructions = {
            SafetyLevel.SAFE_ONLY: "Only apply fixes marked as SAFE with confidence >= 0.9",
            SafetyLevel.REVIEW_REQUIRED: "Apply fixes marked as SAFE or LIKELY_SAFE with confidence >= 0.8",
            SafetyLevel.MANUAL_APPROVAL: "Apply all approved fixes regardless of safety rating"
        }

        fixes_text = "\n".join([
            f"- Fix {i+1}: {fix.get('title', 'Unknown fix')} (Confidence: {fix.get('confidence', 0)}, Safety: {fix.get('safety_rating', 'UNKNOWN')})"
            for i, fix in enumerate(fixes)
        ])

        system_prompt = f"""You are an expert code editor specializing in automated code fixes. Your task is to apply approved fixes to code while maintaining functionality and code quality.

Safety Level: {safety_level.value}
{safety_instructions[safety_level]}

Apply fixes by:
1. **Preserving Code Structure**: Maintain indentation, formatting, and style
2. **Applying Changes Sequentially**: Apply fixes in order, accounting for line number changes
3. **Validating Syntax**: Ensure the resulting code is syntactically correct
4. **Maintaining Functionality**: Don't break existing functionality
5. **Adding Comments**: Add brief comments explaining significant changes

Return the complete modified file content with a summary of applied changes:
{{
    "success": true,
    "modified_content": "<complete file content with fixes applied>",
    "applied_fixes": [
        {{
            "fix_id": "<fix identifier>",
            "title": "<fix title>",
            "lines_modified": [<line_numbers>],
            "change_summary": "<brief description of what was changed>"
        }}
    ],
    "skipped_fixes": [
        {{
            "fix_id": "<fix identifier>",
            "reason": "<why it was skipped>"
        }}
    ],
    "warnings": ["<any warnings about the changes>"]
}}

If any fix cannot be safely applied, skip it and include it in skipped_fixes with a reason."""

        user_prompt = f"""File Path: {file_path}
Language: {language}

Original Code:
```{language}
{file_content}
```

Fixes to Apply:
{fixes_text}

Please apply the approved fixes following the safety level: {safety_level.value}"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]


# Convenience functions to get messages by operation type
def get_file_action_messages(
    operation: str,
    language: str,
    file_path: str,
    file_content: str,
    **kwargs
) -> List[Dict[str, str]]:
    """
    Get file action messages for a specific operation type

    Args:
        operation: Type of operation ('review', 'document', 'security-scan', 'fix-suggestions', 'auto-apply')
        language: Programming language
        file_path: Path to the file
        file_content: Complete file content
        **kwargs: Additional arguments specific to each operation

    Returns:
        List of message dictionaries for the LLM

    Raises:
        ValueError: If operation type is not supported
    """
    operation_map = {
        'review': FileActionPrompts.get_code_review_messages,
        'document': FileActionPrompts.get_documentation_generation_messages,
        'security-scan': FileActionPrompts.get_security_scan_messages,
        'fix-suggestions': FileActionPrompts.get_fix_suggestions_messages,
        'auto-apply': FileActionPrompts.get_auto_apply_messages
    }

    if operation not in operation_map:
        raise ValueError(f"Unsupported operation: {operation}. Supported operations: {list(operation_map.keys())}")

    return operation_map[operation](language, file_path, file_content, **kwargs)
