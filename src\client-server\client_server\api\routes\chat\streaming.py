"""
HTTP streaming functionality for chat.

This module contains the HTTPStreamEventEmitter class and related
functionality for handling Server-Sent Events (SSE) streaming.
"""

import json
import time
from typing import Any


class HTTPStreamEventEmitter:
    """
    Replaces socketio.emit functionality with Server-Sent Events (SSE) for HTTP streaming.
    Formats events as SSE data that can be streamed to the client.
    """

    def __init__(self, request_id: str):
        self.request_id = request_id
        self.events = []

    async def emit(self, event_name: str, data: dict[str, Any], to: str = None):
        """
        Emit an event by adding it to the events list.
        This replaces the socketio.emit functionality.
        """
        event_data = {
            "event": event_name,
            "data": data,
            "timestamp": time.time()
        }
        self.events.append(event_data)

    def format_as_sse(self, event_data: dict[str, Any]) -> str:
        """Format event data as Server-Sent Events (SSE) format"""
        event_name = event_data.get("event", "message")
        data = event_data.get("data", {})

        # Format as SSE
        sse_data = f"event: {event_name}\ndata: {json.dumps(data)}\n\n"
        return sse_data

    def get_events(self) -> list[dict[str, Any]]:
        """Get all accumulated events"""
        return self.events.copy()

    def clear_events(self):
        """Clear all accumulated events"""
        self.events.clear()
