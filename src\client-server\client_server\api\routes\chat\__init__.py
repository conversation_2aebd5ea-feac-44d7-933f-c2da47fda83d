"""
Chat module for handling chat functionality.

This module contains all chat-related components organized into logical submodules:
- models: Data models and request/response schemas
- handlers: Core chat processing logic
- streaming: HTTP streaming and event emission
- routes: FastAPI route handlers
"""

# Import all components to maintain backward compatibility
from .models import *
from .handlers import *
from .server_handlers import *
from .streaming import *
from .routes import *

__all__ = [
    # Models
    "ChatStreamRequest",
    "ChatStreamChunkAction", 
    "ChatStreamChunk",
    "ChatStreamActionChunk",
    "ChatStreamContentChunk",
    
    # Streaming
    "HTTPStreamEventEmitter",
    
    # Handlers
    "process_local_chat_request_http",
    "format_filepath",
    "ensure_eco_mode_dependencies",

    # Server Handlers
    "process_chat_request_from_server_http",
    
    # Routes
    "chat_stream",
]
