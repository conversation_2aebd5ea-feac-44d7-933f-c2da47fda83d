import atexit
import asyncio
import sys
import os
import time
import threading
from typing import Optional, Dict, Any

from fastapi import Fast<PERSON>I, Request
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from socketio import AsyncServer
from socketio.asgi import <PERSON><PERSON>App
from starlette.middleware.base import BaseHTTPMiddleware

from client_server.api.events.chat import handle_chat_event
from client_server.api.events.chat_stop import handle_chat_stop_event
from client_server.api.events.connection import (
    handle_connect_event,
    handle_disconnect_event,
    cleanup_old_sessions,
)
from client_server.api.events.dependency_install import handle_dependency_install_event
from client_server.api.events.dependency_uninstall import (
    handle_dependency_uninstall_event,
)
from client_server.api.events.fs import (
    handle_get_file_paths_recursive_event,
    handle_get_folder_paths_recursive_event,
)
from client_server.api.events.inline_suggestion import handle_inline_suggestion_event

from client_server.api.events.upload import handle_upload_event, handle_upload_to_cloud_event, handle_sync_to_cloud_event
from client_server.api.events.swagger import handle_swagger_gen_event
from client_server.api.routes import setup_routes
from client_server.core import constants
from client_server.core.logger import LOGGER
from client_server.services.embeddings.utils import EmbeddingInferenceBuilder
from client_server.services.inference.utils import InferenceBuilder
from client_server.services.qdrant_handler import get_db_client
from client_server.services.startup_config import perform_startup_configuration
from client_server.utils.model_selector import ModelSelector
from client_server.utils.timestamp_migration import (
    perform_conditional_timestamp_migration,
)


# -----------------------------------------------------------------------------
# Event Handlers
# -----------------------------------------------------------------------------


async def handle_heartbeat_event(
    sio: AsyncServer, sid: str, data: Optional[Dict[str, Any]] = None
) -> None:
    """Handle client heartbeat to detect alive connections"""
    try:
        await sio.emit(
            "heartbeat_response",
            {"server_time": time.time(), "status": "alive"},
            to=sid,
        )
        LOGGER.debug(f"Heartbeat response sent to session {sid}")
    except Exception as e:
        LOGGER.error(f"Error handling heartbeat for session {sid}: {e}")


async def start_background_tasks() -> None:
    """Start background tasks for session management"""
    try:
        while True:
            await asyncio.sleep(constants.SOCKETIO_SESSION_CLEANUP_INTERVAL)
            await cleanup_old_sessions()
    except Exception as e:
        LOGGER.error(f"Error in background cleanup task: {e}")


# -----------------------------------------------------------------------------
# Middleware
# -----------------------------------------------------------------------------


# Custom CORS middleware specifically for VS Code webviews and Socket.IO
class VSCodeWebviewCORSMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Handle preflight requests
        if request.method == "OPTIONS":
            origin = request.headers.get("origin", "")

            # Create preflight response
            from starlette.responses import Response

            response = Response()

            # Always handle CORS for WebSocket server or HTTP server with Socket.IO paths
            if origin.startswith("vscode-webview://"):
                response.headers["Access-Control-Allow-Origin"] = origin
            else:
                response.headers["Access-Control-Allow-Origin"] = "*"

            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Allow-Methods"] = (
                "GET, POST, PUT, DELETE, OPTIONS"
            )
            response.headers["Access-Control-Allow-Headers"] = (
                "Content-Type, Authorization, X-Requested-With"
            )
            response.headers["Access-Control-Max-Age"] = "3600"

            return response

        response = await call_next(request)

        # Extract origin from request
        origin = request.headers.get("origin", "")

        # ALWAYS override CORS headers - remove any existing ones first to prevent duplicates
        if "Access-Control-Allow-Origin" in response.headers:
            del response.headers["Access-Control-Allow-Origin"]
        if "access-control-allow-origin" in response.headers:
            del response.headers["access-control-allow-origin"]

        # Check if this is a VSCode webview origin
        is_vscode_webview = origin.startswith("vscode-webview://")

        if is_vscode_webview:
            # Allow VSCode webview origins
            response.headers["Access-Control-Allow-Origin"] = origin
        else:
            # For other origins allow all
            response.headers["Access-Control-Allow-Origin"] = "*"

        # Always set these headers
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = (
            "GET, POST, PUT, DELETE, OPTIONS"
        )
        response.headers["Access-Control-Allow-Headers"] = (
            "Content-Type, Authorization, X-Requested-With"
        )

        return response


# -----------------------------------------------------------------------------
# Initialize the servers
# -----------------------------------------------------------------------------

# Define allowed origins (include VSCode webview pattern)
allowed_origins = [
    "*",
    # VSCode webview pattern
    "vscode-webview://*",
    # For development
    "http://localhost:*",
    "http://127.0.0.1:*",
]

# -----------------------------------------------------------------------------
# HTTP Server Setup (FastAPI)
# -----------------------------------------------------------------------------

# Create FastAPI app for HTTP REST API
http_app = FastAPI(title="CodeMate HTTP API")

# Configure CORS for HTTP app
http_app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Add custom middleware for VSCode webview
http_app.add_middleware(VSCodeWebviewCORSMiddleware)

# -----------------------------------------------------------------------------
# WebSocket Server Setup (Socket.IO)
# -----------------------------------------------------------------------------

# Set up Socket.IO server for WebSocket connections
sio = AsyncServer(
    async_mode="asgi",
    cors_allowed_origins=[],  # Disable Socket.IO's CORS completely - we handle it in middleware
    ping_interval=constants.SOCKETIO_PING_INTERVAL,
    ping_timeout=constants.SOCKETIO_PING_TIMEOUT,
    max_http_buffer_size=constants.SOCKETIO_MAX_HTTP_BUFFER_SIZE,
    # Add session persistence and reconnection settings
    always_connect=True,  # Allow reconnections
    engineio_logger=False,  # Disable verbose logging
    # Connection upgrade settings
    transports=["websocket", "polling"],  # Allow fallback to polling if websocket fails
)

# Create ASGI app for Socket.IO
websocket_app = ASGIApp(sio)

# Create a FastAPI wrapper for WebSocket server to apply middleware
websocket_fastapi_app = FastAPI(title="CodeMate WebSocket API")

# Only add custom middleware for VSCode webview to WebSocket app
# Don't add FastAPI CORS middleware to avoid header duplication
websocket_fastapi_app.add_middleware(VSCodeWebviewCORSMiddleware)

# Mount the Socket.IO ASGI app on the FastAPI app
websocket_fastapi_app.mount("/", websocket_app)

# -----------------------------------------------------------------------------
# Attach WS events
# -----------------------------------------------------------------------------


# Helper function to make event handlers
def make_handler(handler):
    async def callback(sid, data=None):
        await handler(sio, sid, data)

    return callback


# Basic
sio.on("connect", handler=make_handler(handle_connect_event))
sio.on("disconnect", handler=make_handler(handle_disconnect_event))

# Chat
sio.on("chat", handler=make_handler(handle_chat_event))
sio.on("chat_stop", handler=make_handler(handle_chat_stop_event))

# Knowledgebase
sio.on("upload", handler=make_handler(handle_upload_event))
sio.on("upload_to_cloud", handler=make_handler(handle_upload_to_cloud_event))
sio.on("sync_to_cloud", handler=make_handler(handle_sync_to_cloud_event))

# Utility
sio.on("inline_suggestion", handler=make_handler(handle_inline_suggestion_event))

# Filesystem
sio.on(
    "get_folder_paths_recursive",
    handler=make_handler(handle_get_folder_paths_recursive_event),
)
sio.on(
    "get_file_paths_recursive",
    handler=make_handler(handle_get_file_paths_recursive_event),
)

sio.on("swagger_gen", handler=make_handler(handle_swagger_gen_event))

sio.on("dependency_install", handler=make_handler(handle_dependency_install_event))
sio.on("dependency_uninstall", handler=make_handler(handle_dependency_uninstall_event))

# Heartbeat handler for connection monitoring
sio.on("heartbeat", handler=make_handler(handle_heartbeat_event))

# -----------------------------------------------------------------------------
# Attach REST routes to HTTP server
# -----------------------------------------------------------------------------

# Set up all routes using the route decorator system
setup_routes(http_app)

# -----------------------------------------------------------------------------
# Run the server
# -----------------------------------------------------------------------------


def setup_qdrant() -> None:
    try:
        get_db_client()
    except Exception:
        LOGGER.warning("Qdrant already runnning, exiting")
        sys.exit(0)


def preload_models_if_needed() -> None:
    # Embedding model
    if constants.PRELOAD_EMBEDDING_MODEL:
        LOGGER.info(f"🔄 Preloading embedding model")
        EmbeddingInferenceBuilder.create()
        LOGGER.success("✅ Successfully loaded embedding model")
    # Chat models
    if constants.PRELOAD_CHAT_MODEL:
        LOGGER.info(f"🔄 Preloading chat model {ModelSelector.chat()}")
        InferenceBuilder.create().ensure_model(ModelSelector.chat())
        LOGGER.success("✅ Successfully loaded chat model")
    # Autocomplete model
    if constants.PRELOAD_INLINE_SUGGESTION_MODEL:
        LOGGER.info(
            f"🔄 Preloading autocomplete model {ModelSelector.inline_suggestion()}"
        )
        InferenceBuilder.create().ensure_model(ModelSelector.inline_suggestion())
        LOGGER.success("✅ Successfully loaded autocomplete model")


async def test() -> None:
    return
    print(await EmbeddingInferenceBuilder.create().generate("Hello, how are you?"))
    exit(0)


def main() -> None:
    os.system("clear || cls")

    asyncio.run(test())

    atexit.register(lambda: InferenceBuilder.dispose())
    atexit.register(lambda: EmbeddingInferenceBuilder.dispose())

    # Perform automatic knowledge base configuration during startup
    perform_startup_configuration()

    # Perform conditional timestamp migration for existing knowledge bases
    perform_conditional_timestamp_migration()

    setup_qdrant()
    preload_models_if_needed()

    # Start background tasks for session management
    def start_background_task() -> None:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(start_background_tasks())

    background_thread = threading.Thread(target=start_background_task, daemon=True)
    background_thread.start()

    LOGGER.info("🚀 Starting dual-port server with enhanced sleep/wake resilience")
    LOGGER.info(f"📡 HTTP REST API server starting on port {constants.HTTP_PORT}")
    LOGGER.info(f"🔌 WebSocket server starting on port {constants.WEBSOCKET_PORT}")

    try:
        # Start both servers concurrently using asyncio
        async def run_servers():
            # Create server configurations
            http_config = uvicorn.Config(
                http_app,
                host="127.0.0.1",
                port=constants.HTTP_PORT,
                proxy_headers=False,
                access_log=False,
                log_config=None,
            )

            websocket_config = uvicorn.Config(
                websocket_fastapi_app,
                host="127.0.0.1",
                port=constants.WEBSOCKET_PORT,
                proxy_headers=False,
                access_log=False,
                log_config=None,
            )

            # Create server instances
            http_server = uvicorn.Server(http_config)
            websocket_server = uvicorn.Server(websocket_config)

            # Run both servers concurrently
            await asyncio.gather(
                http_server.serve(), websocket_server.serve(), return_exceptions=True
            )

        # Run the dual server setup
        asyncio.run(run_servers())

    except Exception as e:
        LOGGER.error(f"❌ Error in main: {e}")
        sys.exit(1)
    LOGGER.success("✅ Servers stopped")


# -----------------------------------------------------------------------------

if __name__ == "__main__":
    main()
