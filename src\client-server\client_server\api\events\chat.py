import asyncio
import json
import os
import time
import traceback
from typing import Any, Literal, AsyncGenerator
from uuid import uuid4

import socketio
from pydantic import BaseModel
import tqdm
import requests
from fastapi.responses import StreamingResponse

from client_server.core.constants import SSL_CERT_FILE
from client_server.core.state import G_BASE_URL, G_CANCELLED_REQUEST_IDS
from client_server.services.dependencies import DependencyStatus
from client_server.services.dependencies.ollama import OllamaDependency
from client_server.services.inference.utils import InferenceBuilder
from client_server.services.tokenization.utils import (
    TokenizationBuilder,
    ensure_messages_within_token_limit,
)
from client_server.utils.actions.context_search import (
    process_context_search,
    _perform_local_search,
)
from client_server.utils.actions.folder_search import (
    _perform_folder_search,
    process_folder_search,
)
from client_server.utils.actions.swagger_search import (
    _perform_swagger_search,
    process_swagger_search,
)
from client_server.utils.actions.web_search import process_web_search
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import (
    log_memory_usage,
    log_file_stats,
    log_execution_time,
    log_api_call_stats,
    log_system_info,
    log_operation_stats,
)
from client_server.utils.model_selector import ModelSelector
from client_server.utils.models.knowledgebase import QdrantKnowledgeBase
from client_server.utils.actions.utils import SearchReferences
from client_server.utils.path_selector import PathSelector
from client_server.utils.platform_detector import PlatformDetector
from client_server.api.events.connection import update_session_activity


# -----------------------------------------------------------------------------
# Constants
# -----------------------------------------------------------------------------

MESSAGE_DELIMITER = "<__!!__END__!!__>"


# -----------------------------------------------------------------------------
# HTTP Event Emitter for Streaming
# -----------------------------------------------------------------------------


class HTTPStreamEventEmitter:
    """
    Replaces socketio.emit functionality with Server-Sent Events (SSE) for HTTP streaming.
    Formats events as SSE data that can be streamed to the client.
    """

    def __init__(self, request_id: str):
        self.request_id = request_id
        self.events = []

    async def emit(self, event_name: str, data: dict[str, Any], to: str = None):
        """
        Emit an event by adding it to the events list.
        This replaces the socketio.emit functionality.
        """
        event_data = {
            "event": event_name,
            "data": data,
            "timestamp": time.time()
        }
        self.events.append(event_data)

    def format_as_sse(self, event_data: dict[str, Any]) -> str:
        """Format event data as Server-Sent Events (SSE) format"""
        event_name = event_data.get("event", "message")
        data = event_data.get("data", {})

        # Format as SSE
        sse_data = f"event: {event_name}\ndata: {json.dumps(data)}\n\n"
        return sse_data

    def get_events(self) -> list[dict[str, Any]]:
        """Get all accumulated events"""
        return self.events.copy()

    def clear_events(self):
        """Clear all accumulated events"""
        self.events.clear()


# -----------------------------------------------------------------------------
# Models
# -----------------------------------------------------------------------------


class ChatMessageContext(BaseModel):
    id: str
    type: Literal[
        "file",
        "folder",
        "commit",
        "terminal",
        "errors",
        "warnings",
        # Knowledgebases
        "codebase",
        "docs",
        "git",
        "swagger",
    ]
    name: str
    path: str | None = None
    content: str = ""
    kbid: str | None = None
    sub: str


class ChatMessage(BaseModel):
    role: str
    content: str | list[str] = ""
    context: list[ChatMessageContext] = []
    action: dict[str, Any] | list[dict[str, Any]] | None = None
    action_id: str | list[str] | None = None


class ChatPayload(BaseModel):
    mode: Literal["NORMAL", "ECO", "PRO"] = "NORMAL"
    messages: list[ChatMessage] = []
    provider: str | dict[str, Any]
    conversation_id: str
    session__: str | None = None
    request_id: str
    web_search: bool
    provide_followups: bool


class ChatStreamChunkAction(BaseModel):
    id: str
    name: str
    type: Literal["function"]
    args: dict[str, Any]


class ChatStreamChunk(BaseModel):
    type: Literal["content", "action"]
    memory_id: str
    chunk_index: int = 0
    # action: dict[str, any] = {}


class ChatStreamContentChunk(ChatStreamChunk):
    type: Literal["content"]
    content: str


class ChatStreamActionChunk(ChatStreamChunk):
    type: Literal["action"]
    action: list[ChatStreamChunkAction]


# -----------------------------------------------------------------------------
# Agentic function/action handling
# -----------------------------------------------------------------------------


async def _handle_action(
    action: ChatStreamChunkAction,
    search_references: SearchReferences,
    session: str,
    sio: socketio.AsyncServer,
    sid: str,
    request_id: str,
):
    """
    Route and handle different types of actions
    Args:
        action_data (ChatStreamChunkAction): The action data to be processed

    Returns:
        list[dict[str, Any]]: The processed action results in form of messages
    """
    start_time = time.time()
    LOGGER.info(f"Handling action: {action.name} with ID: {action.id}")
    LOGGER.debug(f"Action args: {action.args}")

    try:
        # Emit searching event before any search happens
        await sio.emit(
            "chat_response_searching",
            data={"request_id": request_id, "action_id": action.id},
            to=sid,
        )

        match action.name:
            case "context_search":
                LOGGER.debug(
                    f"Processing context search - Query: {action.args.get('query', '')[:100]}..."
                )
                result = await process_context_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    kbid=action.args["kbid"],
                    search_references=search_references,
                )

            case "folder_search":
                LOGGER.debug(
                    f"Processing folder search - Path: {action.args.get('folder_path', '')}"
                )
                result = await process_folder_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    folder_path=action.args["folder_path"],
                    index_name=action.args["kbid"],
                    search_references=search_references,
                )

            case "web_search":
                LOGGER.debug(
                    f"Processing web search - Query: {action.args.get('query', '')[:100]}..."
                )
                result = await process_web_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    session=session,
                    search_references=search_references,
                )

            case "swagger_search":
                LOGGER.debug(
                    f"Processing swagger search - KBID: {action.args.get('kbid', '')}"
                )
                result = await process_swagger_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    search_refrences=search_references,
                    kbid=action.args["kbid"],
                )

            case _:
                LOGGER.warning(f"Unknown action type: {action.name}")
                raise ValueError(f"Unknown action type: {action.name}")

        execution_time = log_execution_time(
            f"_handle_action({action.name})", start_time
        )
        LOGGER.debug(f"Action {action.name} completed successfully")
        return result

    except Exception as e:
        execution_time = time.time() - start_time
        LOGGER.error(
            f"Error handling action {action.name} after {execution_time:.2f}s: {e}"
        )
        LOGGER.error(f"Action error traceback: {traceback.format_exc()}")
        raise


# -----------------------------------------------------------------------------
# Chat stream handling
# -----------------------------------------------------------------------------


async def _process_action_chunk(
    chunk: str,
    search_references: SearchReferences,
    session: str,
    sio: socketio.AsyncServer,
    sid: str,
    request_id: str,
):
    """Process the action response"""
    start_time = time.time()
    LOGGER.debug(f"Processing action chunk - Length: {len(chunk)} characters")

    try:
        chunk_data = ChatStreamActionChunk.model_validate_json(chunk)
        LOGGER.debug(
            f"Action chunk memory_id: {chunk_data.memory_id}, actions count: {len(chunk_data.action)}"
        )

        chunk_message_list = []
        references = []

        for i, chnk_data in enumerate(chunk_data.action):
            LOGGER.debug(
                f"Processing action {i+1}/{len(chunk_data.action)}: {chnk_data.name}"
            )
            action_start = time.time()

            data, ref = await _handle_action(
                chnk_data,
                search_references=search_references,
                session=session,
                sio=sio,
                sid=sid,
                request_id=request_id,
            )
            action_time = time.time() - action_start
            LOGGER.debug(f"Action {chnk_data.name} processed in {action_time:.2f}s")

            chunk_message_list.append(data)
            references.append(ref)

        response = [
            {
                "role": "assistant",
                "action": list(map(lambda x: x[0]["action"], chunk_message_list)),
            },
            {
                "role": "action",
                "content": list(map(lambda x: x[1]["content"], chunk_message_list)),
                "action_id": list(map(lambda x: x[1]["action_id"], chunk_message_list)),
            },
        ]

        log_execution_time("_process_action_chunk", start_time)
        LOGGER.debug(
            f"Action chunk processing completed - Generated {len(response)} response messages"
        )

        return response, references[0] if references else None

    except json.JSONDecodeError as e:
        LOGGER.error(f"Failed to parse action chunk: {chunk[:200]}...")
        LOGGER.error(f"JSON decode error: {e}")
        raise
    except Exception as e:
        LOGGER.error(f"Error processing action chunk: {e}")
        LOGGER.error(f"Action chunk error traceback: {traceback.format_exc()}")
        raise


async def _process_content_chunk(
    chunk: str,
    search_references: SearchReferences,
    session: str,
    sio: socketio.AsyncServer,
    sid: str,
    request_id: str,
):
    """Process individual messages from the stream"""
    start_time = time.time()
    LOGGER.debug(f"Processing content chunk - Length: {len(chunk)} characters")

    try:
        chunk_data = ChatStreamContentChunk.model_validate_json(chunk)
        LOGGER.debug(
            f"Content chunk - Memory ID: {chunk_data.memory_id}, "
            f"Index: {chunk_data.chunk_index}, Content length: {len(chunk_data.content)}"
        )

        result = chunk_data.model_dump(), []
        log_execution_time("_process_content_chunk", start_time)
        return result

    except json.JSONDecodeError as e:
        LOGGER.error(f"Failed to parse content chunk: {chunk[:200]}...")
        LOGGER.error(f"JSON decode error: {e}")
        raise
    except Exception as e:
        LOGGER.error(f"Error processing content chunk: {e}")
        LOGGER.error(f"Content chunk error traceback: {traceback.format_exc()}")
        raise


def format_filepath(filepath: str):
    """Format file path based on platform"""
    if PlatformDetector.is_macos():
        formatted_path = f"/{filepath}"
        LOGGER.debug(f"Formatted macOS path: {filepath} -> {formatted_path}")
        return formatted_path
    LOGGER.debug(f"Using original path: {filepath}")
    return filepath


async def process_chat_request_from_server(
    sio: socketio.AsyncServer,
    sid: str,
    payload: ChatPayload,
):
    """Process the chat request and handle the response streaming"""
    overall_start_time = time.time()
    log_memory_usage("chat_request_start")

    LOGGER.info(
        f"Processing chat request from server - Session: {sid}, "
        f"Request ID: {payload.request_id}, Mode: {payload.mode}"
    )
    LOGGER.info(
        f"Message count: {len(payload.messages)}, "
        f"Web search enabled: {payload.web_search}, "
        f"Conversation ID: {payload.conversation_id}"
    )

    # Log system information
    system_info = log_system_info()

    context_processing_start = time.time()
    total_contexts = sum(len(message.context) for message in payload.messages)
    LOGGER.info(f"Processing {total_contexts} contexts across all messages")

    for msg_idx, message in enumerate(payload.messages):
        LOGGER.debug(
            f"Processing message {msg_idx+1}/{len(payload.messages)} with {len(message.context)} contexts"
        )

        for ctx_idx, context in enumerate(message.context):
            context_start = time.time()
            LOGGER.debug(
                f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type} - {context.name}"
            )

            if context.type == "file":
                if context.path:
                    try:
                        file_path = format_filepath(context.path)
                        file_size = log_file_stats(file_path)

                        read_start = time.time()
                        with open(file_path, "r", encoding="utf-8") as e:
                            context.content = e.read()
                        read_time = time.time() - read_start

                        content_length = len(context.content)
                        content_lines = context.content.count("\n") + 1
                        LOGGER.debug(
                            f"File read - Path: {file_path}, Size: {file_size} bytes, "
                            f"Content: {content_length} chars, {content_lines} lines, "
                            f"Read time: {read_time:.2f}s"
                        )
                    except Exception as e:
                        LOGGER.error(f"Failed to read file {context.path}: {e}")
                        context.content = ""
                else:
                    LOGGER.warning(f"File context without path: {context.name}")

            elif context.type == "swagger":
                LOGGER.info(f"Processing swagger context - KBID: {context.kbid}")

                base_url = G_BASE_URL.get().general

                try:
                    # Emit searching event before swagger search
                    await sio.emit(
                        "chat_response_searching",
                        data={"request_id": payload.request_id},
                        to=sid,
                    )

                    # Generate queries
                    api_start = time.time()
                    response = requests.post(
                        f"{base_url}/swagger/generate_queries",
                        json={"query": message.content},
                        timeout=30000,
                    )
                    api_time = time.time() - api_start

                    log_api_call_stats(
                        f"{base_url}/swagger/generate_queries",
                        "POST",
                        response.status_code,
                        api_time,
                        len(response.content) if response.content else 0,
                    )

                    if response.status_code == 200:
                        query_list = response.json()
                        LOGGER.info(
                            f"Generated {len(query_list)} swagger queries: {query_list}"
                        )

                        search_results = []
                        search_start = time.time()

                        for query_idx, query in enumerate(query_list):
                            LOGGER.debug(
                                f"Processing swagger query {query_idx+1}/{len(query_list)}: {query}"
                            )
                            query_search_start = time.time()

                            search_result = await _perform_swagger_search(
                                query=query, index_name=context.kbid or ""
                            )
                            query_search_time = time.time() - query_search_start

                            LOGGER.debug(
                                f"Query {query_idx+1} returned {len(search_result)} results in {query_search_time:.2f}s"
                            )
                            search_results.extend(search_result)

                        search_time = time.time() - search_start
                        LOGGER.info(
                            f"Swagger search completed - Total results: {len(search_results)} in {search_time:.2f}s"
                        )

                        final_search_results_map = {}
                        for endpoint in search_results:
                            final_search_results_map[endpoint["path"]] = endpoint
                        final_search_results = list(final_search_results_map.values())
                        final_search_results.sort(key=lambda x: x["path"])

                        context.content = json.dumps(final_search_results)
                        with open("swagger_search_results.json", "w") as f:
                            json.dump(final_search_results, f, indent=2)

                        search_references = SearchReferences(payload.request_id)
                        for endpoint in final_search_results:
                            search_references.add_search_result(
                                path=endpoint["path"],
                                type="file",
                                content=json.dumps(endpoint, indent=2),
                                name=endpoint["path"],
                            )
                        print("Swagger search references added to search_references")
                        await sio.emit(
                            "chat_response_references",
                            data=search_references.get_search_result(),
                            to=sid,
                        )
                        await asyncio.sleep(0.001)
                        LOGGER.debug("Emitted swagger search references to client")
                    else:
                        LOGGER.error(
                            f"Swagger query generation failed with status {response.status_code}"
                        )

                except Exception as e:
                    LOGGER.error(f"Error processing swagger context: {e}")
                    LOGGER.error(
                        f"Swagger context error traceback: {traceback.format_exc()}"
                    )

            context_time = time.time() - context_start
            LOGGER.debug(f"Context {context.type} processed in {context_time:.2f}s")

    context_processing_time = time.time() - context_processing_start
    LOGGER.info(f"Context processing completed in {context_processing_time:.2f}s")
    log_memory_usage("after_context_processing")

    final_payload = payload.model_dump(exclude_none=True)
    LOGGER.debug(f"Final payload size: {len(str(final_payload))} characters")

    try:
        assistant_response = ""

        # generate chat stream
        base_url = G_BASE_URL.get().chat
        LOGGER.info(f"Starting chat stream request to {base_url}/chat")
        request_start = time.time()

        response = requests.post(
            f"{base_url}/chat",
            json=final_payload,
            headers={
                "Content-Type": "application/json",
                "x-session": payload.session__,
            },
            stream=True,
            timeout=30000,
            verify=SSL_CERT_FILE,
        )

        request_time = time.time() - request_start
        LOGGER.info(f"Chat stream request initiated in {request_time:.2f}s")

        log_api_call_stats(
            f"{base_url}/chat", "POST", response.status_code, request_time
        )

        if response.status_code != 200 or "text/plain" not in response.headers.get(
            "Content-Type", ""
        ):
            LOGGER.error(
                f"Unexpected response - Status: {response.status_code}, "
                f"Content-Type: {response.headers.get('Content-Type', 'unknown')}"
            )
            LOGGER.error(f"Response content: {response.text[:500]}...")
            raise ValueError("Invalid response from chat server")

        chunk_text = ""
        chunk_count = 0
        content_chunks = 0
        action_chunks = 0
        total_content_length = 0

        CHUNK_HANDLERS = {
            "action": _process_action_chunk,
            "content": _process_content_chunk,
        }

        LOGGER.info("Starting stream processing")
        stream_processing_start = time.time()

        for buffer in response.iter_content(chunk_size=256):
            LOGGER.info(
                f"Request {payload.request_id} cancelled: {G_CANCELLED_REQUEST_IDS.get()}"
            )
            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, skipping chunk")
                break

            if not buffer:
                continue
            text = buffer.decode("utf-8")
            chunk_text += text

            while MESSAGE_DELIMITER in chunk_text:
                chunk, chunk_text = chunk_text.split(MESSAGE_DELIMITER, 1)
                chunk = chunk.strip()
                if not chunk:
                    continue

                chunk_count += 1
                chunk_processing_start = time.time()

                try:
                    processed_chunk = ChatStreamChunk.model_validate_json(chunk)
                    LOGGER.debug(
                        f"Processing chunk {chunk_count}: {processed_chunk.type}"
                    )

                    if processed_chunk.type == "content":
                        content_chunks += 1
                        content_chunk = ChatStreamContentChunk.model_validate_json(
                            chunk
                        )
                        assistant_response += content_chunk.content
                        total_content_length += len(content_chunk.content)

                        LOGGER.debug(
                            f"Content chunk {content_chunks}: {content_chunk.content}"
                        )

                    if processed_chunk.type in CHUNK_HANDLERS:
                        if processed_chunk.type == "action":
                            action_chunks += 1
                            LOGGER.debug(f"Processing action chunk {action_chunks}")

                        search_references = SearchReferences(
                            request_id=payload.request_id
                        )
                        handler_start = time.time()

                        new_messages, search_references = await CHUNK_HANDLERS[
                            processed_chunk.type
                        ](
                            chunk,
                            search_references,
                            payload.session__,
                            sio,
                            sid,
                            payload.request_id,
                        )

                        handler_time = time.time() - handler_start
                        LOGGER.debug(f"Chunk handler completed in {handler_time:.2f}s")

                        if search_references:
                            ref_count = len(
                                search_references.get_search_result().get("results", [])
                            )
                            LOGGER.debug(f"Emitting {ref_count} search references")
                            await sio.emit(
                                "chat_response_references",
                                data=search_references.get_search_result(),
                                # to=sid,
                            )
                            await asyncio.sleep(1)
                            print("Search references emitted to client")

                        final_response = {
                            "request_id": payload.request_id,
                            "type": processed_chunk.type,
                            "conversation_id": processed_chunk.memory_id,
                            "data": new_messages,
                        }

                        await asyncio.sleep(0.001)
                        await sio.emit("chat_response", data=final_response, to=sid)

                        if processed_chunk.type == "action":
                            LOGGER.info("Action chunk processed, ending stream")
                            return
                    else:
                        LOGGER.warning(f"Unknown chunk type: {processed_chunk.type}")

                except Exception as e:
                    LOGGER.error(f"Error processing chunk {chunk_count}: {e}")
                    LOGGER.error(f"Chunk content: {chunk[:200]}...")
                    continue

                chunk_processing_time = time.time() - chunk_processing_start
                LOGGER.debug(
                    f"Chunk {chunk_count} processed in {chunk_processing_time:.3f}s"
                )

        stream_processing_time = time.time() - stream_processing_start
        LOGGER.info(
            f"Stream processing completed - Total chunks: {chunk_count}, "
            f"Content chunks: {content_chunks}, Action chunks: {action_chunks}, "
            f"Total content length: {total_content_length} chars, "
            f"Processing time: {stream_processing_time:.2f}s"
        )

        # api call for gathering follow-ups
        LOGGER.info("Starting follow-up generation")
        last_user_message_content = ""
        for message in reversed(payload.messages):
            if message.role == "user":
                last_user_message_content = message.content
                break

        LOGGER.debug(
            f"Last user message for follow-ups: {str(last_user_message_content)[:100]}..."
        )
        await asyncio.sleep(0.5)

        if payload.provide_followups:
            try:
                followup_start = time.time()
                follow_up_response = requests.post(
                    f"{base_url}/chat/follow-up",
                    json={
                        "messages": [
                            {"role": "user", "content": last_user_message_content},
                            {"role": "assistant", "content": assistant_response},
                        ]
                    },
                    timeout=30000,
                    verify=SSL_CERT_FILE,
                )
                followup_time = time.time() - followup_start

                log_api_call_stats(
                    f"{base_url}/chat/follow-up",
                    "POST",
                    follow_up_response.status_code,
                    followup_time,
                    (
                        len(follow_up_response.content)
                        if follow_up_response.content
                        else 0
                    ),
                )

                if follow_up_response.status_code != 200:
                    LOGGER.error(
                        f"Follow-up generation failed with status {follow_up_response.status_code}"
                    )
                    raise ValueError("Failed to get follow-ups")

                data = follow_up_response.json()
                follow_ups = data.get("data", [])
                LOGGER.info(
                    f"Generated {len(follow_ups)} follow-up suggestions in {followup_time:.2f}s"
                )

                await sio.emit(
                    "chat_response_follow_ups",
                    data={
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    },
                    to=sid,
                )
                LOGGER.debug("Follow-ups emitted to client")

            except Exception as e:
                LOGGER.error(f"Error during follow-ups generation: {e}")
                LOGGER.error(f"Follow-up error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)

        await sio.emit(
            "chat_response_end", data={"request_id": payload.request_id}, to=sid
        )

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("chat_request_complete")

        LOGGER.info(
            f"Chat request completed successfully - Total time: {overall_time:.2f}s, "
            f"Response length: {len(assistant_response)} chars"
        )

    except Exception as e:
        overall_time = time.time() - overall_start_time
        LOGGER.error(f"Error during chat request after {overall_time:.2f}s: {e}")
        LOGGER.error(f"Chat request error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)
        await sio.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            },
            to=sid,
        )
        raise


async def process_local_chat_request(
    sio: socketio.AsyncServer, sid: str, payload: ChatPayload
):
    """Process chat request locally using ECO mode"""
    overall_start_time = time.time()
    log_memory_usage("local_chat_start")

    LOGGER.info(
        f"Processing local chat request - Session: {sid}, "
        f"Request ID: {payload.request_id}, Messages: {len(payload.messages)}"
    )

    sequence_messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant developed by CodeMate AI that can answer questions and help with tasks.",
        }
    ]
    search_results = {
        "request_id": payload.request_id,
        "results": [],
    }

    try:
        message_processing_start = time.time()
        total_contexts = sum(len(message.context) for message in payload.messages)
        LOGGER.info(f"Processing {total_contexts} contexts in local mode")

        for msg_idx, message in enumerate(payload.messages):
            LOGGER.debug(
                f"Processing message {msg_idx+1}/{len(payload.messages)}: {message.role}"
            )

            if message.role == "user":
                for ctx_idx, context in enumerate(message.context):
                    context_start = time.time()
                    LOGGER.debug(
                        f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type}"
                    )

                    formatted_context = ""
                    additional_content = ""

                    if context.type == "file":
                        if context.path:
                            try:
                                file_size = log_file_stats(context.path)
                                read_start = time.time()

                                with open(context.path, "r", encoding="utf-8") as e:
                                    context.content = e.read()

                                read_time = time.time() - read_start
                                content_length = len(context.content)
                                LOGGER.debug(
                                    f"File read - Path: {context.path}, Size: {file_size} bytes, "
                                    f"Content: {content_length} chars, Read time: {read_time:.2f}s"
                                )
                            except Exception as e:
                                LOGGER.error(f"Failed to read file {context.path}: {e}")
                                context.content = ""

                        formatted_context = (
                            f"File Name: {context.name}\n"
                            f"File Path: {context.path}\n"
                            f"File Content:\n{context.content}\n\n"
                        )

                    elif context.type == "terminal":
                        formatted_context = f"Terminal:\n{context.content}\n\n"

                    elif context.type == "warnings":
                        formatted_context = f"Warnings:\n{context.content}\n\n"

                    elif context.type == "errors":
                        formatted_context = f"Errors:\n{context.content}\n\n"

                    elif context.type == "commit":
                        formatted_context = (
                            f"COMMIT INFORMATION:\n{context.content}\n\n"
                        )

                    elif context.type == "folder":
                        LOGGER.info(
                            f"Processing folder context - Path: {context.path}, KBID: {context.kbid}"
                        )

                        if not context.path:
                            LOGGER.warning(f"Ignoring folder without path: {context}")
                            continue

                        if QdrantKnowledgeBase.exists_id(context.kbid or ""):
                            LOGGER.info("Knowledge base found for folder search")
                            # Emit searching event before folder search
                            await sio.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id},
                                to=sid,
                            )
                            search_start = time.time()

                            folder_contexts = await _perform_folder_search(
                                query=message.content.replace(
                                    f"<__$__{context.id}__$__>", context.path or ""
                                ),
                                index_name=context.kbid or "",
                                folder_path=context.path or "",
                                is_local=True,
                            )

                            search_time = time.time() - search_start
                            LOGGER.info(
                                f"Folder search completed - Found {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                text = folder_context["content"]["text"]
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                additional_content += f"FileName: {_filename} | FilePath: {_filepath} \n {text}\n"
                        else:
                            LOGGER.warning(
                                f"Knowledge base not found for ID: {context.kbid}"
                            )
                        print("Emitting folder search results")
                        await sio.emit(
                            "chat_response_references", data=search_results, to=sid
                        )
                        print("Folder search results emitted to client")
                        await asyncio.sleep(0.001)

                    elif context.type in ["docs", "codebase", "git"]:
                        LOGGER.info(
                            f"Processing {context.type} context - Name: {context.name}, KBID: {context.kbid}"
                        )

                        formatted_context = f"{context.type} {context.name}"
                        additional_content = (
                            f"\n\n[ Attached Knowledgebases:\n"
                            f"Name: {context.name}    |    KNOWLEDGEBASE ID: {context.kbid}]\n\n"
                        )

                        search_query = message.content.replace(
                            f"<__$__{context.id}__$__>",
                            context.path or context.name or "",
                        )

                        if context.type == "codebase":
                            # Emit searching event before codebase search
                            await sio.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id},
                                to=sid,
                            )
                            search_start = time.time()
                            folder_contexts = await _perform_folder_search(
                                query=search_query,
                                index_name=context.kbid or "",
                                folder_path="",
                                is_local=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Codebase search completed - {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                text = folder_context["content"]["text"]
                                additional_content += text + "\n\n"

                            print("Emitting codebase search results")
                            await sio.emit(
                                "chat_response_references", data=search_results, to=sid
                            )
                            print("Codebase search results emitted to client")
                            await asyncio.sleep(0.001)

                        elif context.type == "docs":
                            # Emit searching event before docs search
                            await sio.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id},
                                to=sid,
                            )
                            search_start = time.time()
                            docs_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context.kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Docs search completed - {len(docs_contexts)} results in {search_time:.2f}s"
                            )

                            for docs_context in docs_contexts:
                                text = docs_context["content"]["text"]
                                additional_content += text + "\n\n"

                        elif context.type == "github":
                            # Emit searching event before git search
                            await sio.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id},
                                to=sid,
                            )
                            search_start = time.time()
                            git_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context.kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.info(
                                f"GitHub search completed - {len(git_contexts)} results in {search_time:.2f}s"
                            )
                            LOGGER.debug(
                                f"First GitHub context: {git_contexts[0] if git_contexts else 'None'}"
                            )

                            for git_context in git_contexts:
                                text = git_context["content"]["text"]
                                additional_content += text + "\n\n"

                    # Replace context placeholders in message content
                    message.content = message.content or ""
                    message.content = (
                        message.content.replace(
                            f"<__$__{context.id}__$__>", formatted_context
                        )
                        + additional_content
                    )

                    context_time = time.time() - context_start
                    LOGGER.debug(
                        f"Context {context.type} processed in {context_time:.2f}s"
                    )

                sequence_messages.append(
                    {
                        "role": "user",
                        "content": message.content,
                    }
                )

                content_length = len(message.content)
                LOGGER.debug(f"Added user message with {content_length} characters")
            else:
                sequence_messages.append(
                    {"role": "assistant", "content": message.content}
                )
                LOGGER.debug(f"Added assistant message")

        message_processing_time = time.time() - message_processing_start
        LOGGER.info(f"Message processing completed in {message_processing_time:.2f}s")

        # Apply token limiting to fit within the 70k token limit
        token_limiting_start = time.time()
        original_message_count = len(sequence_messages)

        sequence_messages = ensure_messages_within_token_limit(
            tokenizer=TokenizationBuilder.create(),
            messages=sequence_messages,
            max_tokens=70000,
            model=ModelSelector.chat(),
        )

        token_limiting_time = time.time() - token_limiting_start
        final_message_count = len(sequence_messages)

        LOGGER.info(
            f"Token limiting completed - Original: {original_message_count} messages, "
            f"Final: {final_message_count} messages, Time: {token_limiting_time:.2f}s"
        )

        if original_message_count != final_message_count:
            LOGGER.warning(
                f"Truncated {original_message_count - final_message_count} messages due to token limit"
            )

    except Exception as e:
        LOGGER.error(f"Error during local chat message processing: {e}")
        LOGGER.error(f"Local chat processing error traceback: {traceback.format_exc()}")

        await sio.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            },
            to=sid,
        )
        await sio.emit(
            "chat_response_end", data={"request_id": payload.request_id}, to=sid
        )
        return

    try:
        inference_start = time.time()
        LOGGER.info("Starting local LLM inference")
        log_memory_usage("before_local_inference")

        llm_backend = InferenceBuilder.create()
        selected_model = ModelSelector.chat()
        LOGGER.info(f"Using local model: {selected_model}")

        llm_response = llm_backend.stream(
            model=selected_model,
            messages=sequence_messages,
        )

        chat_id = str(uuid4())
        chunk_count = 0
        total_response_length = 0

        LOGGER.info(f"Starting response streaming with chat ID: {chat_id}")

        for i, chunk in enumerate(llm_response):
            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, skipping chunk")
                break

            if len(chunk.choices) <= 0:
                continue

            if chunk.choices[0].finish_reason is not None:
                LOGGER.info(
                    f"Stream finished with reason: {chunk.choices[0].finish_reason}"
                )
                break

            if chunk.choices[0].delta.content is not None:
                chunk_count += 1
                content = chunk.choices[0].delta.content
                total_response_length += len(content)

                if chunk_count <= 5:  # Log first few chunks
                    LOGGER.debug(f"Chunk {chunk_count}: {len(content)} chars")

                await sio.emit(
                    "chat_response",
                    data={
                        "type": "content",
                        "request_id": payload.request_id,
                        "conversation_id": "",
                        "data": {
                            "type": "content",
                            "memory_id": chat_id,
                            "chunk_index": i,
                            "content": content,
                        },
                    },
                )

        inference_time = time.time() - inference_start
        avg_time_per_chunk = inference_time / chunk_count if chunk_count > 0 else 0

        LOGGER.info(
            f"Local inference completed - Chunks: {chunk_count}, "
            f"Total response: {total_response_length} chars, "
            f"Time: {inference_time:.2f}s, "
            f"Avg per chunk: {avg_time_per_chunk:.3f}s"
        )

        await sio.emit(
            "chat_response_end", data={"request_id": payload.request_id}, to=sid
        )

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("local_chat_complete")

        LOGGER.info(f"Local chat request completed successfully in {overall_time:.2f}s")

    except Exception as e:
        inference_time = (
            time.time() - inference_start if "inference_start" in locals() else 0
        )
        LOGGER.error(
            f"Error during local chat inference after {inference_time:.2f}s: {e}"
        )
        LOGGER.error(f"Local inference error traceback: {traceback.format_exc()}")

        error_response = {
            "request_id": payload.request_id,
            "error": "An error occurred while processing the chat request.",
        }
        await sio.emit("chat_response_error", data=error_response, to=sid)
        raise


def read_all_files(folder_path: str):
    """Read all files in a folder recursively"""
    start_time = time.time()
    LOGGER.debug(f"Reading all files from folder: {folder_path}")

    file_contents = []
    file_count = 0
    total_size = 0
    error_count = 0

    try:
        for root, _, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = log_file_stats(file_path)
                    total_size += file_size

                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        file_contents.append({"file_name": file, "content": content})
                        file_count += 1

                except Exception as e:
                    error_count += 1
                    LOGGER.warning(f"Failed to read file {file_path}: {e}")

        read_time = time.time() - start_time
        LOGGER.info(
            f"Folder read completed - Files: {file_count}, "
            f"Total size: {total_size / 1024 / 1024:.2f} MB, "
            f"Errors: {error_count}, Time: {read_time:.2f}s"
        )

        return file_contents

    except Exception as e:
        LOGGER.error(f"Error reading folder {folder_path}: {e}")
        return []


# -----------------------------------------------------------------------------
# Chat event handler
# -----------------------------------------------------------------------------


def ensure_eco_mode_dependencies():
    """Ensure Ollama dependencies are installed for ECO mode"""
    dependency_start = time.time()
    LOGGER.info("Starting Ollama dependency setup for ECO mode")
    log_memory_usage("before_ollama_setup")

    ollama_dependency = OllamaDependency(PathSelector.get_cache_path() / "ollama")

    # Create a progress bar for installation progress
    pbar = None
    installation_start = None

    try:
        status_count = 0
        for status, data in ollama_dependency.ensure():
            status_count += 1

            if status == DependencyStatus.READY:
                LOGGER.info(f"Ollama ready: {data}")
                continue

            if status == DependencyStatus.INSTALLED:
                if installation_start:
                    install_time = time.time() - installation_start
                    LOGGER.info(
                        f"Ollama installed successfully in {install_time:.2f}s: {data}"
                    )
                else:
                    LOGGER.info(f"Ollama installed: {data}")
                continue

            if status == DependencyStatus.ERROR:
                LOGGER.error(f"Ollama error: {data}")
                continue

            if status == DependencyStatus.MISSING:
                LOGGER.warning(f"Ollama missing: {data}")
                continue

            if status == DependencyStatus.INSTALLING:
                if installation_start is None:
                    installation_start = time.time()
                    LOGGER.info("Starting Ollama installation")

                # Handle progress updates
                if isinstance(data, float):
                    if pbar is None:
                        pbar = tqdm.tqdm(total=100, desc="Installing Ollama")
                    pbar.n = round(data)
                    pbar.refresh()

                    if round(data) % 10 == 0:  # Log every 10%
                        LOGGER.debug(f"Ollama installation progress: {data:.1f}%")
                else:
                    LOGGER.info(f"Ollama installation: {data}")
                continue

            # Handle other status messages
            if isinstance(data, str):
                LOGGER.debug(f"Ollama status {status}: {data}")

    except Exception as e:
        LOGGER.error(f"Error during Ollama dependency setup: {e}")
        LOGGER.error(f"Ollama setup error traceback: {traceback.format_exc()}")
        raise
    finally:
        if pbar is not None:
            pbar.close()

    dependency_time = time.time() - dependency_start
    log_memory_usage("after_ollama_setup")
    LOGGER.info(f"Ollama dependency setup completed in {dependency_time:.2f}s")


async def handle_chat_event(sio: socketio.AsyncServer, sid: str, data: dict[str, Any]):
    """Main handler for chat events"""
    start_time = time.time()
    log_memory_usage("chat_event_start")

    # Update session activity to track this interaction
    update_session_activity(sid)

    LOGGER.info(f"Handling chat event for session {sid}")

    try:
        # Validate payload
        validation_start = time.time()
        payload = ChatPayload.model_validate(data)
        validation_time = time.time() - validation_start

        LOGGER.info(
            f"Chat payload validated - Mode: {payload.mode}, "
            f"Request ID: {payload.request_id}, "
            f"Messages: {len(payload.messages)}, "
            f"Validation time: {validation_time:.3f}s"
        )

        # Log payload statistics
        total_contexts = sum(len(msg.context) for msg in payload.messages)
        context_types = {}
        for msg in payload.messages:
            for ctx in msg.context:
                context_types[ctx.type] = context_types.get(ctx.type, 0) + 1

        LOGGER.debug(f"Context distribution: {context_types}")
        LOGGER.debug(f"Total contexts: {total_contexts}")

        # Route based on mode
        mode_processing_start = time.time()

        match payload.mode:
            case "NORMAL":
                LOGGER.info("Processing in NORMAL mode (server-based)")
                await process_chat_request_from_server(sio, sid, payload)

            case "PRO":
                LOGGER.info("Processing in PRO mode (server-based)")
                await process_chat_request_from_server(sio, sid, payload)

            case "ECO":
                LOGGER.info("Processing in ECO mode (local)")
                dependency_start = time.time()
                ensure_eco_mode_dependencies()
                dependency_time = time.time() - dependency_start
                LOGGER.debug(f"ECO mode dependencies ensured in {dependency_time:.2f}s")

                await process_local_chat_request(sio, sid, payload)

            case _:
                LOGGER.error(f"Invalid chat mode: {payload.mode}")
                raise ValueError(f"Invalid mode: {payload.mode}")

        mode_processing_time = time.time() - mode_processing_start
        total_time = time.time() - start_time
        final_memory = log_memory_usage("chat_event_complete")

        # Update session activity again after successful completion
        update_session_activity(sid)

        LOGGER.info(
            f"Chat event completed successfully - Mode: {payload.mode}, "
            f"Mode processing: {mode_processing_time:.2f}s, "
            f"Total time: {total_time:.2f}s"
        )

    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error in chat event handler after {total_time:.2f}s: {e}")
        LOGGER.error(f"Chat handler error traceback: {traceback.format_exc()}")
        log_memory_usage("chat_event_error")

        # The individual processors handle their own error emissions
        # so we don't need to emit error here
        raise


# -----------------------------------------------------------------------------
# HTTP Streaming Chat Functions
# -----------------------------------------------------------------------------


async def _handle_action_http(
    action: ChatStreamChunkAction,
    search_references: SearchReferences,
    session: str,
    event_emitter: HTTPStreamEventEmitter,
    request_id: str,
):
    """
    HTTP version of _handle_action.
    Route and handle different types of actions for HTTP streaming.
    """
    start_time = time.time()
    LOGGER.info(f"Handling action: {action.name} with ID: {action.id}")
    LOGGER.debug(f"Action args: {action.args}")

    try:
        # Emit searching event before any search happens
        await event_emitter.emit(
            "chat_response_searching",
            data={"request_id": request_id, "action_id": action.id}
        )

        match action.name:
            case "context_search":
                LOGGER.debug(
                    f"Processing context search - Query: {action.args.get('query', '')[:100]}..."
                )
                result = await process_context_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    kbid=action.args["kbid"],
                    search_references=search_references,
                )

            case "folder_search":
                LOGGER.debug(
                    f"Processing folder search - Path: {action.args.get('folder_path', '')}"
                )
                result = await process_folder_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    folder_path=action.args["folder_path"],
                    index_name=action.args["kbid"],
                    search_references=search_references,
                )

            case "web_search":
                LOGGER.debug(
                    f"Processing web search - Query: {action.args.get('query', '')[:100]}..."
                )
                result = await process_web_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    session=session,
                    search_references=search_references,
                )

            case "swagger_search":
                LOGGER.debug(
                    f"Processing swagger search - KBID: {action.args.get('kbid', '')}"
                )
                result = await process_swagger_search(
                    tool_id=action.id,
                    query=action.args["query"],
                    search_refrences=search_references,
                    kbid=action.args["kbid"],
                )

            case _:
                LOGGER.warning(f"Unknown action type: {action.name}")
                raise ValueError(f"Unknown action type: {action.name}")

        execution_time = log_execution_time(
            f"_handle_action_http({action.name})", start_time
        )
        LOGGER.debug(f"Action {action.name} completed successfully")
        return result

    except Exception as e:
        execution_time = time.time() - start_time
        LOGGER.error(
            f"Error handling action {action.name} after {execution_time:.2f}s: {e}"
        )
        LOGGER.error(f"Action error traceback: {traceback.format_exc()}")
        raise


async def _process_action_chunk_http(
    chunk: str,
    search_references: SearchReferences,
    session: str,
    event_emitter: HTTPStreamEventEmitter,
    request_id: str,
):
    """HTTP version of _process_action_chunk"""
    start_time = time.time()
    LOGGER.debug(f"Processing action chunk - Length: {len(chunk)} characters")

    try:
        chunk_data = ChatStreamActionChunk.model_validate_json(chunk)
        LOGGER.debug(
            f"Action chunk memory_id: {chunk_data.memory_id}, actions count: {len(chunk_data.action)}"
        )

        chunk_message_list = []
        references = []

        for i, chnk_data in enumerate(chunk_data.action):
            LOGGER.debug(
                f"Processing action {i+1}/{len(chunk_data.action)}: {chnk_data.name}"
            )
            action_start = time.time()

            data, ref = await _handle_action_http(
                chnk_data,
                search_references=search_references,
                session=session,
                event_emitter=event_emitter,
                request_id=request_id,
            )
            action_time = time.time() - action_start
            LOGGER.debug(f"Action {chnk_data.name} processed in {action_time:.2f}s")

            chunk_message_list.append(data)
            references.append(ref)

        response = [
            {
                "role": "assistant",
                "action": list(map(lambda x: x[0]["action"], chunk_message_list)),
            },
            {
                "role": "action",
                "content": list(map(lambda x: x[1]["content"], chunk_message_list)),
                "action_id": list(map(lambda x: x[1]["action_id"], chunk_message_list)),
            },
        ]

        log_execution_time("_process_action_chunk_http", start_time)
        LOGGER.debug(
            f"Action chunk processing completed - Generated {len(response)} response messages"
        )

        return response, references[0] if references else None

    except json.JSONDecodeError as e:
        LOGGER.error(f"Failed to parse action chunk: {chunk[:200]}...")
        LOGGER.error(f"JSON decode error: {e}")
        raise
    except Exception as e:
        LOGGER.error(f"Error processing action chunk: {e}")
        LOGGER.error(f"Action chunk error traceback: {traceback.format_exc()}")
        raise


async def _process_content_chunk_http(
    chunk: str,
    search_references: SearchReferences,
    session: str,
    event_emitter: HTTPStreamEventEmitter,
    request_id: str,
):
    """HTTP version of _process_content_chunk"""
    start_time = time.time()
    LOGGER.debug(f"Processing content chunk - Length: {len(chunk)} characters")

    try:
        chunk_data = ChatStreamContentChunk.model_validate_json(chunk)
        LOGGER.debug(
            f"Content chunk - Memory ID: {chunk_data.memory_id}, "
            f"Index: {chunk_data.chunk_index}, Content length: {len(chunk_data.content)}"
        )

        result = chunk_data.model_dump(), []
        log_execution_time("_process_content_chunk_http", start_time)
        return result

    except json.JSONDecodeError as e:
        LOGGER.error(f"Failed to parse content chunk: {chunk[:200]}...")
        LOGGER.error(f"JSON decode error: {e}")
        raise
    except Exception as e:
        LOGGER.error(f"Error processing content chunk: {e}")
        LOGGER.error(f"Content chunk error traceback: {traceback.format_exc()}")
        raise


async def process_chat_request_from_server_http(
    event_emitter: HTTPStreamEventEmitter,
    payload: ChatPayload,
) -> AsyncGenerator[str, None]:
    """
    HTTP streaming version of process_chat_request_from_server.
    Yields Server-Sent Events instead of using WebSocket emissions.
    """
    overall_start_time = time.time()
    log_memory_usage("http_chat_request_start")

    LOGGER.info(
        f"Processing HTTP chat request from server - "
        f"Request ID: {payload.request_id}, Mode: {payload.mode}"
    )
    LOGGER.info(
        f"Message count: {len(payload.messages)}, "
        f"Web search enabled: {payload.web_search}, "
        f"Conversation ID: {payload.conversation_id}"
    )

    # Log system information
    system_info = log_system_info()

    context_processing_start = time.time()
    total_contexts = sum(len(message.context) for message in payload.messages)
    LOGGER.info(f"Processing {total_contexts} contexts across all messages")

    for msg_idx, message in enumerate(payload.messages):
        LOGGER.debug(
            f"Processing message {msg_idx+1}/{len(payload.messages)} with {len(message.context)} contexts"
        )

        for ctx_idx, context in enumerate(message.context):
            context_start = time.time()
            LOGGER.debug(
                f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type} - {context.name}"
            )

            if context.type == "file":
                if context.path:
                    try:
                        file_path = format_filepath(context.path)
                        file_size = log_file_stats(file_path)

                        read_start = time.time()
                        with open(file_path, "r", encoding="utf-8") as e:
                            context.content = e.read()
                        read_time = time.time() - read_start

                        content_length = len(context.content)
                        content_lines = context.content.count("\n") + 1
                        LOGGER.debug(
                            f"File read - Path: {file_path}, Size: {file_size} bytes, "
                            f"Content: {content_length} chars, {content_lines} lines, "
                            f"Read time: {read_time:.2f}s"
                        )
                    except Exception as e:
                        LOGGER.error(f"Failed to read file {context.path}: {e}")
                        context.content = ""
                else:
                    LOGGER.warning(f"File context without path: {context.name}")

            elif context.type == "swagger":
                LOGGER.info(f"Processing swagger context - KBID: {context.kbid}")

                base_url = G_BASE_URL.get().general

                try:
                    # Emit searching event before swagger search
                    await event_emitter.emit(
                        "chat_response_searching",
                        data={"request_id": payload.request_id}
                    )
                    yield event_emitter.format_as_sse({
                        "event": "chat_response_searching",
                        "data": {"request_id": payload.request_id}
                    })

                    # Generate queries
                    api_start = time.time()
                    response = requests.post(
                        f"{base_url}/swagger/generate_queries",
                        json={"query": message.content},
                        timeout=30000,
                    )
                    api_time = time.time() - api_start

                    log_api_call_stats(
                        f"{base_url}/swagger/generate_queries",
                        "POST",
                        response.status_code,
                        api_time,
                        len(response.content) if response.content else 0,
                    )

                    if response.status_code == 200:
                        query_list = response.json()
                        LOGGER.info(
                            f"Generated {len(query_list)} swagger queries: {query_list}"
                        )

                        search_results = []
                        search_start = time.time()

                        for query_idx, query in enumerate(query_list):
                            LOGGER.debug(
                                f"Processing swagger query {query_idx+1}/{len(query_list)}: {query}"
                            )
                            query_search_start = time.time()

                            search_result = await _perform_swagger_search(
                                query=query, index_name=context.kbid or ""
                            )
                            query_search_time = time.time() - query_search_start

                            LOGGER.debug(
                                f"Query {query_idx+1} returned {len(search_result)} results in {query_search_time:.2f}s"
                            )
                            search_results.extend(search_result)

                        search_time = time.time() - search_start
                        LOGGER.info(
                            f"Swagger search completed - Total results: {len(search_results)} in {search_time:.2f}s"
                        )

                        final_search_results_map = {}
                        for endpoint in search_results:
                            final_search_results_map[endpoint["path"]] = endpoint
                        final_search_results = list(final_search_results_map.values())
                        final_search_results.sort(key=lambda x: x["path"])

                        context.content = json.dumps(final_search_results)
                        with open("swagger_search_results.json", "w") as f:
                            json.dump(final_search_results, f, indent=2)

                        search_references = SearchReferences(payload.request_id)
                        for endpoint in final_search_results:
                            search_references.add_search_result(
                                path=endpoint["path"],
                                type="file",
                                content=json.dumps(endpoint, indent=2),
                                name=endpoint["path"],
                            )
                        print("Swagger search references added to search_references")

                        # Emit search references
                        await event_emitter.emit(
                            "chat_response_references",
                            data=search_references.get_search_result()
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": search_references.get_search_result()
                        })
                        await asyncio.sleep(0.001)
                        LOGGER.debug("Emitted swagger search references to client")
                    else:
                        LOGGER.error(
                            f"Swagger query generation failed with status {response.status_code}"
                        )

                except Exception as e:
                    LOGGER.error(f"Error processing swagger context: {e}")
                    LOGGER.error(
                        f"Swagger context error traceback: {traceback.format_exc()}"
                    )

            context_time = time.time() - context_start
            LOGGER.debug(f"Context {context.type} processed in {context_time:.2f}s")

    context_processing_time = time.time() - context_processing_start
    LOGGER.info(f"Context processing completed in {context_processing_time:.2f}s")
    log_memory_usage("after_context_processing")

    final_payload = payload.model_dump(exclude_none=True)
    LOGGER.debug(f"Final payload size: {len(str(final_payload))} characters")

    try:
        assistant_response = ""

        # generate chat stream
        base_url = G_BASE_URL.get().chat
        LOGGER.info(f"Starting chat stream request to {base_url}/chat")
        request_start = time.time()

        response = requests.post(
            f"{base_url}/chat",
            json=final_payload,
            headers={
                "Content-Type": "application/json",
                "x-session": payload.session__,
            },
            stream=True,
            timeout=30000,
            verify=SSL_CERT_FILE,
        )

        request_time = time.time() - request_start
        LOGGER.info(f"Chat stream request initiated in {request_time:.2f}s")

        log_api_call_stats(
            f"{base_url}/chat", "POST", response.status_code, request_time
        )

        if response.status_code != 200 or "text/plain" not in response.headers.get(
            "Content-Type", ""
        ):
            LOGGER.error(
                f"Unexpected response - Status: {response.status_code}, "
                f"Content-Type: {response.headers.get('Content-Type', 'unknown')}"
            )
            LOGGER.error(f"Response content: {response.text[:500]}...")
            raise ValueError("Invalid response from chat server")

        chunk_text = ""
        chunk_count = 0
        content_chunks = 0
        action_chunks = 0
        total_content_length = 0

        CHUNK_HANDLERS = {
            "action": _process_action_chunk_http,
            "content": _process_content_chunk_http,
        }

        LOGGER.info("Starting stream processing")
        stream_processing_start = time.time()

        for buffer in response.iter_content(chunk_size=256):
            LOGGER.info(
                f"Request {payload.request_id} cancelled: {G_CANCELLED_REQUEST_IDS.get()}"
            )
            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, skipping chunk")
                break

            if not buffer:
                continue
            text = buffer.decode("utf-8")
            chunk_text += text

            while MESSAGE_DELIMITER in chunk_text:
                chunk, chunk_text = chunk_text.split(MESSAGE_DELIMITER, 1)
                chunk = chunk.strip()
                if not chunk:
                    continue

                chunk_count += 1
                chunk_processing_start = time.time()

                try:
                    processed_chunk = ChatStreamChunk.model_validate_json(chunk)
                    LOGGER.debug(
                        f"Processing chunk {chunk_count}: {processed_chunk.type}"
                    )

                    if processed_chunk.type == "content":
                        content_chunks += 1
                        content_chunk = ChatStreamContentChunk.model_validate_json(
                            chunk
                        )
                        assistant_response += content_chunk.content
                        total_content_length += len(content_chunk.content)

                        LOGGER.debug(
                            f"Content chunk {content_chunks}: {content_chunk.content}"
                        )

                    if processed_chunk.type in CHUNK_HANDLERS:
                        if processed_chunk.type == "action":
                            action_chunks += 1
                            LOGGER.debug(f"Processing action chunk {action_chunks}")

                        search_references = SearchReferences(
                            request_id=payload.request_id
                        )
                        handler_start = time.time()

                        new_messages, search_references = await CHUNK_HANDLERS[
                            processed_chunk.type
                        ](
                            chunk,
                            search_references,
                            payload.session__,
                            event_emitter,
                            payload.request_id,
                        )

                        handler_time = time.time() - handler_start
                        LOGGER.debug(f"Chunk handler completed in {handler_time:.2f}s")

                        if search_references:
                            ref_count = len(
                                search_references.get_search_result().get("results", [])
                            )
                            LOGGER.debug(f"Emitting {ref_count} search references")

                            await event_emitter.emit(
                                "chat_response_references",
                                data=search_references.get_search_result()
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_references",
                                "data": search_references.get_search_result()
                            })
                            await asyncio.sleep(1)
                            print("Search references emitted to client")

                        final_response = {
                            "request_id": payload.request_id,
                            "type": processed_chunk.type,
                            "conversation_id": processed_chunk.memory_id,
                            "data": new_messages,
                        }

                        await asyncio.sleep(0.001)

                        await event_emitter.emit("chat_response", data=final_response)
                        yield event_emitter.format_as_sse({
                            "event": "chat_response",
                            "data": final_response
                        })

                        if processed_chunk.type == "action":
                            LOGGER.info("Action chunk processed, ending stream")
                            return
                    else:
                        LOGGER.warning(f"Unknown chunk type: {processed_chunk.type}")

                except Exception as e:
                    LOGGER.error(f"Error processing chunk {chunk_count}: {e}")
                    LOGGER.error(f"Chunk content: {chunk[:200]}...")
                    continue

                chunk_processing_time = time.time() - chunk_processing_start
                LOGGER.debug(
                    f"Chunk {chunk_count} processed in {chunk_processing_time:.3f}s"
                )

        stream_processing_time = time.time() - stream_processing_start
        LOGGER.info(
            f"Stream processing completed - Total chunks: {chunk_count}, "
            f"Content chunks: {content_chunks}, Action chunks: {action_chunks}, "
            f"Total content length: {total_content_length} chars, "
            f"Processing time: {stream_processing_time:.2f}s"
        )

        # api call for gathering follow-ups
        LOGGER.info("Starting follow-up generation")
        last_user_message_content = ""
        for message in reversed(payload.messages):
            if message.role == "user":
                last_user_message_content = message.content
                break

        LOGGER.debug(
            f"Last user message for follow-ups: {str(last_user_message_content)[:100]}..."
        )
        await asyncio.sleep(0.5)

        if payload.provide_followups:
            try:
                followup_start = time.time()
                follow_up_response = requests.post(
                    f"{base_url}/chat/follow-up",
                    json={
                        "messages": [
                            {"role": "user", "content": last_user_message_content},
                            {"role": "assistant", "content": assistant_response},
                        ]
                    },
                    timeout=30000,
                    verify=SSL_CERT_FILE,
                )
                followup_time = time.time() - followup_start

                log_api_call_stats(
                    f"{base_url}/chat/follow-up",
                    "POST",
                    follow_up_response.status_code,
                    followup_time,
                    (
                        len(follow_up_response.content)
                        if follow_up_response.content
                        else 0
                    ),
                )

                if follow_up_response.status_code != 200:
                    LOGGER.error(
                        f"Follow-up generation failed with status {follow_up_response.status_code}"
                    )
                    raise ValueError("Failed to get follow-ups")

                data = follow_up_response.json()
                follow_ups = data.get("data", [])
                LOGGER.info(
                    f"Generated {len(follow_ups)} follow-up suggestions in {followup_time:.2f}s"
                )

                await event_emitter.emit(
                    "chat_response_follow_ups",
                    data={
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    }
                )
                yield event_emitter.format_as_sse({
                    "event": "chat_response_follow_ups",
                    "data": {
                        "request_id": payload.request_id,
                        "follow_ups": follow_ups,
                    }
                })
                LOGGER.debug("Follow-ups emitted to client")

            except Exception as e:
                LOGGER.error(f"Error during follow-ups generation: {e}")
                LOGGER.error(f"Follow-up error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("http_chat_request_complete")

        LOGGER.info(
            f"HTTP chat request completed successfully - Total time: {overall_time:.2f}s, "
            f"Response length: {len(assistant_response)} chars"
        )

    except Exception as e:
        overall_time = time.time() - overall_start_time
        LOGGER.error(f"Error during HTTP chat request after {overall_time:.2f}s: {e}")
        LOGGER.error(f"HTTP chat request error traceback: {traceback.format_exc()}")

        await asyncio.sleep(0.5)
        await event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        })
        raise


async def process_local_chat_request_http(
    event_emitter: HTTPStreamEventEmitter,
    payload: ChatPayload
) -> AsyncGenerator[str, None]:
    """
    HTTP streaming version of process_local_chat_request.
    Process chat request locally using ECO mode with HTTP streaming.
    """
    overall_start_time = time.time()
    log_memory_usage("http_local_chat_start")

    LOGGER.info(
        f"Processing HTTP local chat request - "
        f"Request ID: {payload.request_id}, Messages: {len(payload.messages)}"
    )

    sequence_messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant developed by CodeMate AI that can answer questions and help with tasks.",
        }
    ]
    search_results = {
        "request_id": payload.request_id,
        "results": [],
    }

    try:
        message_processing_start = time.time()
        total_contexts = sum(len(message.context) for message in payload.messages)
        LOGGER.info(f"Processing {total_contexts} contexts in local mode")

        for msg_idx, message in enumerate(payload.messages):
            LOGGER.debug(
                f"Processing message {msg_idx+1}/{len(payload.messages)}: {message.role}"
            )

            if message.role == "user":
                for ctx_idx, context in enumerate(message.context):
                    context_start = time.time()
                    LOGGER.debug(
                        f"Processing context {ctx_idx+1}/{len(message.context)}: {context.type}"
                    )

                    formatted_context = ""
                    additional_content = ""

                    if context.type == "file":
                        if context.path:
                            try:
                                file_size = log_file_stats(context.path)
                                read_start = time.time()

                                with open(context.path, "r", encoding="utf-8") as e:
                                    context.content = e.read()

                                read_time = time.time() - read_start
                                content_length = len(context.content)
                                LOGGER.debug(
                                    f"File read - Path: {context.path}, Size: {file_size} bytes, "
                                    f"Content: {content_length} chars, Read time: {read_time:.2f}s"
                                )
                            except Exception as e:
                                LOGGER.error(f"Failed to read file {context.path}: {e}")
                                context.content = ""

                        formatted_context = (
                            f"File Name: {context.name}\n"
                            f"File Path: {context.path}\n"
                            f"File Content:\n{context.content}\n\n"
                        )

                    elif context.type == "terminal":
                        formatted_context = f"Terminal:\n{context.content}\n\n"

                    elif context.type == "warnings":
                        formatted_context = f"Warnings:\n{context.content}\n\n"

                    elif context.type == "errors":
                        formatted_context = f"Errors:\n{context.content}\n\n"

                    elif context.type == "commit":
                        formatted_context = (
                            f"COMMIT INFORMATION:\n{context.content}\n\n"
                        )

                    elif context.type == "folder":
                        LOGGER.info(
                            f"Processing folder context - Path: {context.path}, KBID: {context.kbid}"
                        )

                        if not context.path:
                            LOGGER.warning(f"Ignoring folder without path: {context}")
                            continue

                        if QdrantKnowledgeBase.exists_id(context.kbid or ""):
                            LOGGER.info("Knowledge base found for folder search")
                            # Emit searching event before folder search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()

                            folder_contexts = await _perform_folder_search(
                                query=message.content.replace(
                                    f"<__$__{context.id}__$__>", context.path or ""
                                ),
                                index_name=context.kbid or "",
                                folder_path=context.path or "",
                                is_local=True,
                            )

                            search_time = time.time() - search_start
                            LOGGER.info(
                                f"Folder search completed - Found {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                text = folder_context["content"]["text"]
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                additional_content += f"FileName: {_filename} | FilePath: {_filepath} \n {text}\n"
                        else:
                            LOGGER.warning(
                                f"Knowledge base not found for ID: {context.kbid}"
                            )
                        print("Emitting folder search results")
                        await event_emitter.emit(
                            "chat_response_references", data=search_results
                        )
                        yield event_emitter.format_as_sse({
                            "event": "chat_response_references",
                            "data": search_results
                        })
                        print("Folder search results emitted to client")
                        await asyncio.sleep(0.001)

                    elif context.type in ["docs", "codebase", "git"]:
                        LOGGER.info(
                            f"Processing {context.type} context - Name: {context.name}, KBID: {context.kbid}"
                        )

                        formatted_context = f"{context.type} {context.name}"
                        additional_content = (
                            f"\n\n[ Attached Knowledgebases:\n"
                            f"Name: {context.name}    |    KNOWLEDGEBASE ID: {context.kbid}]\n\n"
                        )

                        search_query = message.content.replace(
                            f"<__$__{context.id}__$__>",
                            context.path or context.name or "",
                        )

                        if context.type == "codebase":
                            # Emit searching event before codebase search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            folder_contexts = await _perform_folder_search(
                                query=search_query,
                                index_name=context.kbid or "",
                                folder_path="",
                                is_local=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Codebase search completed - {len(folder_contexts)} results in {search_time:.2f}s"
                            )

                            for folder_context in folder_contexts:
                                _filename = os.path.basename(folder_context["file"])
                                _filepath = folder_context["file"]

                                search_results["results"].append(
                                    {
                                        "path": _filepath,
                                        "type": "file",
                                        "name": _filename,
                                        "content": "",
                                    }
                                )

                                text = folder_context["content"]["text"]
                                additional_content += text + "\n\n"

                            print("Emitting codebase search results")
                            await event_emitter.emit(
                                "chat_response_references", data=search_results
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_references",
                                "data": search_results
                            })
                            print("Codebase search results emitted to client")
                            await asyncio.sleep(0.001)

                        elif context.type == "docs":
                            # Emit searching event before docs search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            docs_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context.kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.debug(
                                f"Docs search completed - {len(docs_contexts)} results in {search_time:.2f}s"
                            )

                            for docs_context in docs_contexts:
                                text = docs_context["content"]["text"]
                                additional_content += text + "\n\n"

                        elif context.type == "github":
                            # Emit searching event before git search
                            await event_emitter.emit(
                                "chat_response_searching",
                                data={"request_id": payload.request_id}
                            )
                            yield event_emitter.format_as_sse({
                                "event": "chat_response_searching",
                                "data": {"request_id": payload.request_id}
                            })
                            search_start = time.time()
                            git_contexts = await _perform_local_search(
                                query=search_query,
                                kbid=context.kbid or "",
                                local_embedding_inference=True,
                            )
                            search_time = time.time() - search_start

                            LOGGER.info(
                                f"GitHub search completed - {len(git_contexts)} results in {search_time:.2f}s"
                            )
                            LOGGER.debug(
                                f"First GitHub context: {git_contexts[0] if git_contexts else 'None'}"
                            )

                            for git_context in git_contexts:
                                text = git_context["content"]["text"]
                                additional_content += text + "\n\n"

                    # Replace context placeholders in message content
                    message.content = message.content or ""
                    message.content = (
                        message.content.replace(
                            f"<__$__{context.id}__$__>", formatted_context
                        )
                        + additional_content
                    )

                    context_time = time.time() - context_start
                    LOGGER.debug(
                        f"Context {context.type} processed in {context_time:.2f}s"
                    )

                sequence_messages.append(
                    {
                        "role": "user",
                        "content": message.content,
                    }
                )

                content_length = len(message.content)
                LOGGER.debug(f"Added user message with {content_length} characters")
            else:
                sequence_messages.append(
                    {"role": "assistant", "content": message.content}
                )
                LOGGER.debug(f"Added assistant message")

        message_processing_time = time.time() - message_processing_start
        LOGGER.info(f"Message processing completed in {message_processing_time:.2f}s")

        # Apply token limiting to fit within the 70k token limit
        token_limiting_start = time.time()
        original_message_count = len(sequence_messages)

        sequence_messages = ensure_messages_within_token_limit(
            tokenizer=TokenizationBuilder.create(),
            messages=sequence_messages,
            max_tokens=70000,
            model=ModelSelector.chat(),
        )

        token_limiting_time = time.time() - token_limiting_start
        final_message_count = len(sequence_messages)

        LOGGER.info(
            f"Token limiting completed - Original: {original_message_count} messages, "
            f"Final: {final_message_count} messages, Time: {token_limiting_time:.2f}s"
        )

        if original_message_count != final_message_count:
            LOGGER.warning(
                f"Truncated {original_message_count - final_message_count} messages due to token limit"
            )

    except Exception as e:
        LOGGER.error(f"Error during HTTP local chat message processing: {e}")
        LOGGER.error(f"HTTP local chat processing error traceback: {traceback.format_exc()}")

        await event_emitter.emit(
            "chat_response_error",
            data={
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": {
                "request_id": payload.request_id,
                "error": "An error occurred while processing the chat request.",
            }
        })
        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })
        return

    try:
        inference_start = time.time()
        LOGGER.info("Starting local LLM inference")
        log_memory_usage("before_local_inference")

        llm_backend = InferenceBuilder.create()
        selected_model = ModelSelector.chat()
        LOGGER.info(f"Using local model: {selected_model}")

        llm_response = llm_backend.stream(
            model=selected_model,
            messages=sequence_messages,
        )

        chat_id = str(uuid4())
        chunk_count = 0
        total_response_length = 0

        LOGGER.info(f"Starting response streaming with chat ID: {chat_id}")

        for i, chunk in enumerate(llm_response):
            if payload.request_id in G_CANCELLED_REQUEST_IDS.get():
                LOGGER.info(f"Request {payload.request_id} cancelled, skipping chunk")
                break

            if len(chunk.choices) <= 0:
                continue

            if chunk.choices[0].finish_reason is not None:
                LOGGER.info(
                    f"Stream finished with reason: {chunk.choices[0].finish_reason}"
                )
                break

            if chunk.choices[0].delta.content is not None:
                chunk_count += 1
                content = chunk.choices[0].delta.content
                total_response_length += len(content)

                if chunk_count <= 5:  # Log first few chunks
                    LOGGER.debug(f"Chunk {chunk_count}: {len(content)} chars")

                response_data = {
                    "type": "content",
                    "request_id": payload.request_id,
                    "conversation_id": "",
                    "data": {
                        "type": "content",
                        "memory_id": chat_id,
                        "chunk_index": i,
                        "content": content,
                    },
                }

                await event_emitter.emit("chat_response", data=response_data)
                yield event_emitter.format_as_sse({
                    "event": "chat_response",
                    "data": response_data
                })

        inference_time = time.time() - inference_start
        avg_time_per_chunk = inference_time / chunk_count if chunk_count > 0 else 0

        LOGGER.info(
            f"Local inference completed - Chunks: {chunk_count}, "
            f"Total response: {total_response_length} chars, "
            f"Time: {inference_time:.2f}s, "
            f"Avg per chunk: {avg_time_per_chunk:.3f}s"
        )

        await event_emitter.emit(
            "chat_response_end", data={"request_id": payload.request_id}
        )
        yield event_emitter.format_as_sse({
            "event": "chat_response_end",
            "data": {"request_id": payload.request_id}
        })

        overall_time = time.time() - overall_start_time
        final_memory = log_memory_usage("http_local_chat_complete")

        LOGGER.info(f"HTTP local chat request completed successfully in {overall_time:.2f}s")

    except Exception as e:
        inference_time = (
            time.time() - inference_start if "inference_start" in locals() else 0
        )
        LOGGER.error(
            f"Error during HTTP local chat inference after {inference_time:.2f}s: {e}"
        )
        LOGGER.error(f"HTTP local inference error traceback: {traceback.format_exc()}")

        error_response = {
            "request_id": payload.request_id,
            "error": "An error occurred while processing the chat request.",
        }
        await event_emitter.emit("chat_response_error", data=error_response)
        yield event_emitter.format_as_sse({
            "event": "chat_response_error",
            "data": error_response
        })
        raise


# -----------------------------------------------------------------------------
