"""
CodeLens Prompts for AI-powered code operations

This module contains message templates for different CodeLens operations
following the established pattern of system and user prompts.
"""

from typing import Dict, List, Any


class CodeLensPrompts:
    """Message templates for CodeLens operations"""
    
    @staticmethod
    def get_inline_edit_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for inline code editing
        
        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to be edited
            instructions: User instructions for the edit
        """
        system_prompt = """You are an expert code editor. Your task is to modify the given code according to the user's instructions while maintaining:

1. Code correctness and functionality
2. Consistent style and formatting
3. Proper error handling
4. Best practices for the given programming language
5. Compatibility with the surrounding context

Return only the modified code without explanations or markdown formatting."""

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Target Code to Edit:
```{language}
{target_code}
```

Instructions: {instructions}

Please provide the edited code that follows the instructions while maintaining compatibility with the context."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_debug_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for debugging assistance
        
        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code that needs debugging
            instructions: Specific debugging request or error description
        """
        system_prompt = """You are an expert debugger and code analyst. Your task is to:

1. Identify potential bugs, issues, or problems in the code
2. Explain the root cause of issues
3. Provide specific, actionable solutions
4. Suggest improvements for robustness and error handling
5. Consider edge cases and potential runtime issues

Provide clear explanations and concrete code fixes when applicable."""

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Debug:
```{language}
{target_code}
```

Debug Request: {instructions}

Please analyze the code and provide debugging insights, explanations, and fixes."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_test_generation_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for test generation
        
        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to generate tests for
            instructions: Specific testing requirements or framework preferences
        """
        system_prompt = """You are an expert test engineer. Your task is to generate comprehensive, well-structured tests that:

1. Cover all major code paths and edge cases
2. Follow testing best practices for the given language
3. Use appropriate testing frameworks and patterns
4. Include both positive and negative test cases
5. Test error conditions and boundary values
6. Are readable, maintainable, and properly documented

Generate complete, runnable test code with clear test names and assertions."""

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Test:
```{language}
{target_code}
```

Testing Requirements: {instructions}

Please generate comprehensive tests for the given code, including setup, test cases, and assertions."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_optimization_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for code optimization
        
        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to optimize
            instructions: Specific optimization goals or constraints
        """
        system_prompt = """You are an expert performance engineer and code optimizer. Your task is to:

1. Analyze code for performance bottlenecks and inefficiencies
2. Suggest optimizations for speed, memory usage, and resource consumption
3. Maintain code readability and maintainability
4. Consider algorithmic improvements and data structure optimizations
5. Ensure optimizations don't break functionality
6. Explain the reasoning behind each optimization

Provide optimized code with clear explanations of the improvements made."""

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Optimize:
```{language}
{target_code}
```

Optimization Goals: {instructions}

Please analyze and optimize the code for better performance, explaining the improvements made."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    @staticmethod
    def get_explanation_messages(language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
        """
        Generate messages for code explanation
        
        Args:
            language: Programming language of the code
            context_code: Surrounding code context
            target_code: Code to explain
            instructions: Specific aspects to focus on or level of detail required
        """
        system_prompt = """You are an expert code educator and technical communicator. Your task is to:

1. Provide clear, comprehensive explanations of code functionality
2. Break down complex logic into understandable components
3. Explain the purpose and role of each significant part
4. Describe data flow, control flow, and interactions
5. Highlight important patterns, algorithms, or design decisions
6. Use appropriate technical language while remaining accessible
7. Provide context about why the code is structured as it is

Make your explanations educational and insightful for developers at various skill levels."""

        user_prompt = f"""Language: {language}

Context Code:
```{language}
{context_code}
```

Code to Explain:
```{language}
{target_code}
```

Explanation Focus: {instructions}

Please provide a detailed explanation of how this code works, its purpose, and key implementation details."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]


# Convenience function to get messages by operation type
def get_codelens_messages(operation: str, language: str, context_code: str, target_code: str, instructions: str) -> List[Dict[str, str]]:
    """
    Get CodeLens messages for a specific operation type
    
    Args:
        operation: Type of operation ('edit', 'debug', 'test', 'optimize', 'explain')
        language: Programming language
        context_code: Surrounding code context
        target_code: Target code for the operation
        instructions: User instructions or requirements
    
    Returns:
        List of message dictionaries for the LLM
    
    Raises:
        ValueError: If operation type is not supported
    """
    operation_map = {
        'edit': CodeLensPrompts.get_inline_edit_messages,
        'debug': CodeLensPrompts.get_debug_messages,
        'test': CodeLensPrompts.get_test_generation_messages,
        'optimize': CodeLensPrompts.get_optimization_messages,
        'explain': CodeLensPrompts.get_explanation_messages
    }
    
    if operation not in operation_map:
        raise ValueError(f"Unsupported operation: {operation}. Supported operations: {list(operation_map.keys())}")
    
    return operation_map[operation](language, context_code, target_code, instructions)
