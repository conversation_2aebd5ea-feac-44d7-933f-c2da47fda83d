"""
Security utilities for API key management and validation.

This module provides utilities for securely handling API keys and sensitive configuration.
"""

import os
import re
from typing import Dict, List, Optional, Tuple
from client_server.core.logger import LOGGER


class APIKeyValidator:
    """Utility class for validating API key formats and security."""
    
    # API key format patterns for different providers
    API_KEY_PATTERNS = {
        'openai': r'^sk-[a-zA-Z0-9]{48,}$',
        'anthropic': r'^sk-ant-[a-zA-Z0-9\-_]{95,}$',
        'google': r'^[a-zA-Z0-9\-_]{39}$',
        'azure_openai': r'^[a-fA-F0-9]{32}$'
    }
    
    @classmethod
    def validate_api_key_format(cls, provider: str, api_key: str) -> bool:
        """
        Validate API key format for a specific provider.
        
        Args:
            provider: The provider name (openai, anthropic, google, azure_openai)
            api_key: The API key to validate
            
        Returns:
            True if the format is valid, False otherwise
        """
        if not api_key or not provider:
            return False
            
        pattern = cls.API_KEY_PATTERNS.get(provider.lower())
        if not pattern:
            LOGGER.warning(f"No validation pattern available for provider: {provider}")
            return True  # Allow unknown providers
            
        return bool(re.match(pattern, api_key))
    
    @classmethod
    def validate_all_keys(cls, api_keys: Dict[str, str]) -> Dict[str, bool]:
        """
        Validate format of all provided API keys.
        
        Args:
            api_keys: Dictionary of provider -> api_key
            
        Returns:
            Dictionary of provider -> validation_result
        """
        results = {}
        for provider, api_key in api_keys.items():
            results[provider] = cls.validate_api_key_format(provider, api_key)
            if not results[provider]:
                LOGGER.warning(f"Invalid API key format for provider: {provider}")
        return results
    
    @classmethod
    def mask_api_key(cls, api_key: str, show_chars: int = 4) -> str:
        """
        Mask an API key for logging purposes.
        
        Args:
            api_key: The API key to mask
            show_chars: Number of characters to show at the end
            
        Returns:
            Masked API key string
        """
        if not api_key or len(api_key) <= show_chars:
            return "***"
        return f"***{api_key[-show_chars:]}"


class SecurityChecker:
    """Security checker for API key and configuration validation."""
    
    @staticmethod
    def check_environment_security() -> List[str]:
        """
        Check for common security issues in environment configuration.
        
        Returns:
            List of security warnings/issues found
        """
        warnings = []
        
        # Check for placeholder values
        placeholder_patterns = [
            'your-api-key-here',
            'placeholder',
            'example',
            'test-key',
            'dummy'
        ]
        
        env_vars_to_check = [
            'OPENAI_API_KEY',
            'ANTHROPIC_API_KEY', 
            'GOOGLE_API_KEY',
            'AZURE_OPENAI_API_KEY'
        ]
        
        for env_var in env_vars_to_check:
            value = os.getenv(env_var, '').lower()
            if value:
                for pattern in placeholder_patterns:
                    if pattern in value:
                        warnings.append(f"{env_var} appears to contain a placeholder value")
                        break
        
        # Check for weak API keys (too short)
        for env_var in env_vars_to_check:
            value = os.getenv(env_var, '')
            if value and len(value) < 20:
                warnings.append(f"{env_var} appears to be too short to be a valid API key")
        
        return warnings
    
    @staticmethod
    def get_security_recommendations() -> List[str]:
        """
        Get security recommendations for API key management.
        
        Returns:
            List of security recommendations
        """
        return [
            "Store API keys in environment variables, never in code",
            "Use different API keys for development and production",
            "Regularly rotate your API keys",
            "Monitor API usage and set up billing alerts",
            "Never commit .env files to version control",
            "Use Azure Key Vault or similar for production deployments",
            "Implement proper access controls and least privilege principles",
            "Enable API key restrictions where available (IP, domain, etc.)",
            "Log API key usage for security monitoring",
            "Use encrypted storage for API keys in production"
        ]


def validate_and_log_api_keys(api_keys: Dict[str, str]) -> Tuple[Dict[str, str], List[str]]:
    """
    Validate API keys and return validated keys with any warnings.
    
    Args:
        api_keys: Dictionary of provider -> api_key
        
    Returns:
        Tuple of (validated_api_keys, warnings)
    """
    warnings = []
    validated_keys = {}
    
    # Validate formats
    validation_results = APIKeyValidator.validate_all_keys(api_keys)
    
    for provider, api_key in api_keys.items():
        if validation_results.get(provider, False):
            validated_keys[provider] = api_key
            LOGGER.info(f"Valid API key configured for {provider}: {APIKeyValidator.mask_api_key(api_key)}")
        else:
            warnings.append(f"Invalid API key format for {provider}")
            LOGGER.warning(f"Skipping invalid API key for {provider}")
    
    # Check for security issues
    security_warnings = SecurityChecker.check_environment_security()
    warnings.extend(security_warnings)
    
    if warnings:
        LOGGER.warning(f"Security warnings found: {warnings}")
    
    return validated_keys, warnings


def log_security_recommendations():
    """Log security recommendations for API key management."""
    recommendations = SecurityChecker.get_security_recommendations()
    LOGGER.info("API Key Security Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        LOGGER.info(f"  {i}. {rec}")
