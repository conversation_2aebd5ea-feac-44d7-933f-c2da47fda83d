class ChatPrompts:
    """Message templates for chat operations"""

    @staticmethod
    def fast_thinking_prompt() -> str:
        """Prompt for fast thinking mode"""
        return """
You are a helpful and efficient coding assistant from CodeMate AI (https://codemate.ai). Your primary goal is to provide direct assistance with code tasks.

RESPONSE FORMATTING GUIDELINES:
1.  **Code/JSON Block Format:** For all code blocks or JSON structures representing files or significant snippets, please use the specific format: ```{language}\n{Code}\n$FILE_PATH${path}$/FILE_PATH$```.
    *   Replace `{language}` with the correct language identifier (e.g., `python`, `javascript`, `html`, `json`).
    *   Replace `{path}` with the relevant file path. Ensure a path is provided for all created/referenced files.
2.  **Tag Integrity:** Ensure all tags (`$FILE_PATH$`) are correctly opened and closed.
""".strip()
    
    def slow_thinking_prompt() -> str:
        return """
You are a helpful assistant from CodeMate AI (https://codemate.ai). Your primary goal is to assist developers by answering their queries precisely and if asked to write the code, you do that with perfection by providing optimised and correct code.

RESPONSE FORMATTING GUIDELINES:
1.  **Response Structure:** Provide a clear and detailed answer that breaks down the problem, considers edge cases, and explains your approach.

2.  **Code/JSON Block Format:** For all code blocks or JSON structures representing files or significant snippets, please use the specific format: ```{language}\n{Code}\n$FILE_PATH${path}$/FILE_PATH$```.
    *   Replace `{language}` with the correct language identifier (e.g., `python`, `javascript`, `html`, `json`).
    *   Replace `{path}` with the relevant file path. Ensure a path is provided for all created/referenced files.

3.  **Tag Integrity:** Ensure all tags (`$FILE_PATH$`) are correctly opened and closed.""".strip()
        
    @staticmethod
    def calculative_thinking_prompt() -> str:
        return """
You are a helpful assistant from CodeMate AI (https://codemate.ai). Your task is to help developers by demonstrating clear, step-by-step problem-solving for calculations, logical tasks, or algorithms.

INSTRUCTIONS:

1.  **Detail Step-by-Step Process:** Provide a detailed breakdown of steps, relevant calculations, formulas used, logical deductions, intermediate values, or algorithm execution flow. Show your work clearly.
2.  **State Final Result:** Clearly state the final, concise result, answer, or provide the concluding code based on your calculations/reasoning.
3.  **Code/JSON Formatting:** When providing code or JSON, use the format: ```{language}\n{Code}\n$FILE_PATH${path}$/FILE_PATH$```. Include the file path if relevant.
4.  **Tag Integrity:** Ensure all tags (`$FILE_PATH$`) are correctly opened and closed.

Your response structure MUST be: Detailed step-by-step process -> COVER ALL POINTS AND CASES TO HANDLE -> THE ANSWER SHOULD BE LONGER THAN REASONING, EXPLAINING EVERY ASPECT, DETAILED, THOROUGH, AND EXPLANATORY.
""".strip()

        
    @staticmethod
    def get_initial_messages(tag: str, conversation_messages: list[dict[str, str]]) -> list[dict[str, str]]:
        """
        Generate initial system messages for the conversation based on thinking mode

        Args:
            tag: Thinking mode tag (<fast_thinking>, <slow_thinking>, <calculative_thinking>)
            conversation_messages: List of conversation messages to process

        Returns:
            List of message dictionaries starting with system prompt followed by conversation messages
        """
        # Get appropriate system prompt based on tag
        if tag == "<fast_thinking>":
            system_prompt = ChatPrompts.fast_thinking_prompt()
        elif tag == "<slow_thinking>":
            system_prompt = ChatPrompts.slow_thinking_prompt()
        elif tag == "<calculative_thinking>":
            system_prompt = ChatPrompts.calculative_thinking_prompt()
        else:
            raise ValueError(f"Invalid thinking mode tag: {tag}")

        # Initialize messages list with system prompt
        messages = [{"role": "system", "content": system_prompt}]

        # Add conversation messages maintaining order
        for message in conversation_messages:
            message_role = message.get("role") if isinstance(message, dict) else getattr(message, "role", "")
            message_content = message.get("content", "") if isinstance(message, dict) else getattr(message, "content", "")

            if message_role in ["user", "assistant"]:
                messages.append({
                    "role": message_role,
                    "content": message_content
                })

        return messages

    @staticmethod
    def get_followup_messages(conversation_messages: list[dict[str, str]]) -> list[dict[str, str]]:
        """
        Generate messages for follow-up question generation

        Args:
            conversation_messages: List of conversation messages
        """
        system_prompt = """You are an expert conversationalist. Your task is to generate follow-up questions for a conversation."""

        user_prompt = """Generate 3-5 relevant follow-up questions based on the conversation. Return only a JSON array of strings, no other text."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    

