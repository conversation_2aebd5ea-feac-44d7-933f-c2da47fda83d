"""
FastAPI route handlers for chat functionality.

This module contains the HTTP endpoint handlers for chat streaming.
"""

import json
import time
import traceback
from typing import AsyncGenerator

from fastapi import Request
from fastapi.responses import StreamingResponse

from client_server.api.events.connection import update_session_activity
from client_server.core.logger import LOGGER
from client_server.core.logger.utils import log_memory_usage
from client_server.api.routes import route

from .models import ChatStreamRequest
from .streaming import HTTPStreamEventEmitter
from .handlers import process_local_chat_request_http, ensure_eco_mode_dependencies
from .server_handlers import process_chat_request_from_server_http
from client_server.core.state import G_BASE_URL, G_SESSION_ID


@route("POST", "/chat/stream")
async def chat_stream(request: Request):
    """
    HTTP streaming endpoint for chat functionality.
    Replaces WebSocket chat events with Server-Sent Events (SSE).
    """
    start_time = time.time()
    log_memory_usage("http_chat_start")
    
    try:
        # Parse request body
        body = await request.body()
        data = json.loads(body.decode('utf-8'))
        
        # Get session from headers
        session_id = request.headers.get("x-session", G_SESSION_ID.get())

        LOGGER.info(f"HTTP chat stream request - Session: {session_id}")

        # Update session activity
        update_session_activity(session_id)

        # Validate payload
        validation_start = time.time()
        payload = ChatStreamRequest.model_validate(data)

        # Set session__ field to the session_id for external server requests
        if payload.session__ is None:
            payload.session__ = session_id

        validation_time = time.time() - validation_start
        
        LOGGER.info(
            f"Chat payload validated - Mode: {payload.mode}, "
            f"Request ID: {payload.request_id}, "
            f"Messages: {len(payload.messages)}, "
            f"Validation time: {validation_time:.3f}s"
        )
        
        # Log payload statistics
        total_contexts = sum(len(msg.get("context", [])) for msg in payload.messages)
        context_types = {}
        for msg in payload.messages:
            for ctx in msg.get("context", []):
                ctx_type = ctx.get("type", "unknown") if isinstance(ctx, dict) else "unknown"
                context_types[ctx_type] = context_types.get(ctx_type, 0) + 1
        
        LOGGER.debug(f"Context distribution: {context_types}")
        LOGGER.debug(f"Total contexts: {total_contexts}")
        
        # Create event emitter for this request
        event_emitter = HTTPStreamEventEmitter(payload.request_id)
        
        # Route based on mode and create streaming generator
        async def generate_chat_stream() -> AsyncGenerator[str, None]:
            try:
                mode_processing_start = time.time()
                
                match payload.mode:
                    case "NORMAL":
                        LOGGER.info("Processing in NORMAL mode (server-based)")
                        async for chunk in process_chat_request_from_server_http(event_emitter, payload):
                            yield chunk
                    
                    case "PRO":
                        LOGGER.info("Processing in PRO mode (server-based)")
                        async for chunk in process_chat_request_from_server_http(event_emitter, payload):
                            yield chunk
                    
                    case "ECO":
                        LOGGER.info("Processing in ECO mode (local)")
                        dependency_start = time.time()
                        ensure_eco_mode_dependencies()
                        dependency_time = time.time() - dependency_start
                        LOGGER.debug(f"ECO mode dependencies ensured in {dependency_time:.2f}s")
                        
                        async for chunk in process_local_chat_request_http(event_emitter, payload):
                            yield chunk
                    
                    case _:
                        LOGGER.error(f"Invalid chat mode: {payload.mode}")
                        error_event = {
                            "event": "chat_response_error",
                            "data": {
                                "request_id": payload.request_id,
                                "error": f"Invalid mode: {payload.mode}"
                            }
                        }
                        yield f"event: chat_response_error\ndata: {json.dumps(error_event['data'])}\n\n"
                        return
                
                mode_processing_time = time.time() - mode_processing_start
                total_time = time.time() - start_time
                final_memory = log_memory_usage("http_chat_complete")
                
                # Update session activity again after successful completion
                update_session_activity(session_id)
                
                LOGGER.info(
                    f"HTTP chat stream completed successfully - Mode: {payload.mode}, "
                    f"Mode processing: {mode_processing_time:.2f}s, "
                    f"Total time: {total_time:.2f}s"
                )
                
            except Exception as e:
                total_time = time.time() - start_time
                LOGGER.error(f"Error in HTTP chat stream after {total_time:.2f}s: {e}")
                LOGGER.error(f"HTTP chat error traceback: {traceback.format_exc()}")
                log_memory_usage("http_chat_error")
                
                # Send error event
                error_event = {
                    "event": "chat_response_error",
                    "data": {
                        "request_id": payload.request_id,
                        "error": "An error occurred while processing the chat request."
                    }
                }
                yield f"event: chat_response_error\ndata: {json.dumps(error_event['data'])}\n\n"
        
        # Return streaming response
        return StreamingResponse(
            generate_chat_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except Exception as e:
        total_time = time.time() - start_time
        LOGGER.error(f"Error setting up HTTP chat stream after {total_time:.2f}s: {e}")
        LOGGER.error(f"HTTP chat setup error traceback: {traceback.format_exc()}")
        
        # Return error response
        error_response = {
            "error": "Failed to process chat request",
            "details": str(e)
        }
        
        async def error_stream():
            yield f"event: chat_response_error\ndata: {json.dumps(error_response)}\n\n"
        
        return StreamingResponse(
            error_stream(),
            media_type="text/plain",
            status_code=500
        )
