# CLIENT_SERVER

VS Code extension's local server with dual-port architecture:
- **HTTP REST API**: Port 45213
- **WebSocket connections**: Port 45214

## Prerequisites

- Python 3.11.9
- Make (GNU Make)
  - Windows users: Install via Scoop or Chocolatey

## Quick Start

First time setup:

```bash
make setup
```

This will set up everything you need and start the server.

### API Key Configuration (Optional)

To use external AI providers, configure your API keys:

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your API keys for the providers you want to use

3. Test your configuration:
   ```bash
   python scripts/test_api_keys.py
   ```

See [docs/API_KEY_SETUP.md](docs/API_KEY_SETUP.md) for detailed setup instructions.

## Step-by-Step Setup

1. **Setup Development Environment**
   ```bash
   make setup
   ```
   This creates a virtual environment and installs Oll
2. **Run the Server**
   ```bash
   make
   # or
   make server
   ```

## Development Notes

- The server is compatible only with VS Code extension v3
- Virtual environment is created in `.venv` directory
- Uses Ollama for local model execution

## Project Overview

The Client Server is a background service that powers the CodeMate VS Code extension. It exposes both REST and WebSocket endpoints to handle asynchronous tasks, enabling seamless communication between the extension and the server.

Built with **Python&nbsp;3.11** and **FastAPI**, the server leverages modern asynchronous programming techniques to deliver high performance and responsiveness.

## Key Features

- **Hybrid Communication:** WebSocket events for real-time interaction and RESTful APIs for standard request/response flows.
- **Service-Oriented Architecture:** Clear interfaces and multiple implementations for each service promote loose coupling and extensibility.
- **Asynchronous Processing:** Uses `asyncio`, `ThreadPoolExecutor`, and `ProcessPoolExecutor` to balance I/O-bound and CPU-bound workloads.
- **Code Intelligence:** Language-aware chunkers, context-search tools, and other utilities power advanced AI features such as chat, inline suggestions, and Swagger generation.
- **Knowledge Base Integration:** Local vector storage powered by Qdrant with optional cloud sync.
- **Robust Error Handling:** Errors are logged locally and optionally reported to a cloud service for monitoring.
- **Global State Management:** Thread-safe global state objects track session IDs, client versions, and cancellation tokens.
- **Context-Aware AI:** Automatic token budgeting prevents context overflow before requests are sent to language models.

## Architecture & Design

### Design Principles

The project follows **SOLID**, **DRY**, and **KISS** principles to stay maintainable and easy to extend.

### Service-Oriented Architecture

Each folder in `client_server/services` defines:

1. An `__init__.py` that declares the interface.
2. One-or-more concrete implementations.
3. A `utils.py` builder/factory that picks the best implementation at runtime.

### Concurrency Model

A hybrid pool of processes and threads ensures that heavy CPU tasks (e.g. embedding generation) do not block I/O tasks (e.g. WebSocket streaming).

## Project Structure

```text
client_server/
├── api/          # REST routes & WebSocket events
├── cmd/          # CLI entry points (server, build, etc.)
├── core/         # Decoupled utilities (constants, logging, state)
├── services/     # Business-logic services with pluggable backends
├── swagger/      # Helpers for OpenAPI processing
└── utils/        # Shared helpers (actions, chunkers, models, …)
```

## Tooling & Build Process

• **Poetry** for dependency management.  
• **Makefile** shortcuts for common development tasks.  
• **PyInstaller** packaging script in `client_server/cmd/build.py` to create standalone executables (with optional code-signing on macOS).  
• All binaries and caches live under the per-user `.codemate` directory managed by `PathSelector`.

## Connection Resilience & Sleep/Wake Handling

The server now includes enhanced Socket.IO configuration to better handle laptop sleep/wake scenarios:

### Server-Side Improvements

- **Optimized Ping Settings**: Reduced ping frequency (25s interval) to minimize network activity during sleep while maintaining responsiveness
- **Session State Persistence**: Sessions are tracked and can recover after reconnection
- **Automatic Cleanup**: Old session states are cleaned up periodically to prevent memory leaks
- **Heartbeat Monitoring**: Additional heartbeat mechanism for connection health checks

### Client-Side Recommendations

To maximize connection stability when the laptop goes to sleep, implement the following on the client side:

```javascript
// Example Socket.IO client configuration for reconnection resilience
// Note: WebSocket server runs on port 45214
const socket = io("http://localhost:45214", {
  // Reconnection settings
  reconnection: true,
  reconnectionAttempts: 10,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  maxReconnectionAttempts: 10,
  timeout: 20000,

  // Transport settings for better resilience
  transports: ["websocket", "polling"],

  // Query parameter to indicate reconnection
  query: {
    reconnect: "true",
  },
});

// Handle reconnection events
socket.on("connect", () => {
  console.log("Connected to server");
});

socket.on("reconnect", (attemptNumber) => {
  console.log("Reconnected after", attemptNumber, "attempts");
});

socket.on("disconnect", (reason) => {
  console.log("Disconnected:", reason);
  if (reason === "io server disconnect") {
    // Server initiated disconnect, reconnect manually
    socket.connect();
  }
});

// Handle server's connection confirmation
socket.on("connection_confirmed", (data) => {
  console.log("Connection confirmed:", data);
  if (data.is_reconnection) {
    console.log("Successfully reconnected after sleep/wake");
  }
});

// Handle reconnection status from server
socket.on("reconnection_status", (data) => {
  console.log("Reconnection status:", data);
  if (data.recovered) {
    console.log(`Reconnected after ${data.disconnect_duration}s offline`);
  }
});

// Implement heartbeat to maintain connection health
setInterval(() => {
  socket.emit("heartbeat");
}, 30000); // Send heartbeat every 30 seconds

socket.on("heartbeat_response", (data) => {
  console.log("Server heartbeat response:", data.status);
});
```

### Best Practices

1. **Handle Graceful Degradation**: Always implement fallback mechanisms for when the connection is lost
2. **Store Request State**: Keep track of pending requests and retry them after reconnection
3. **User Feedback**: Inform users about connection status and reconnection attempts
4. **Timeout Handling**: Implement reasonable timeouts for requests to avoid hanging operations

### Connection Events

The server now emits these additional events:

- `connection_confirmed`: Sent when connection is established (includes reconnection info)
- `reconnection_status`: Sent during reconnection with recovery details
- `heartbeat_response`: Response to client heartbeat pings

### Troubleshooting Sleep/Wake Issues

If you're still experiencing disconnections:

1. Check your system's power management settings
2. Ensure your network adapter isn't being turned off during sleep
3. Consider using the polling transport as a fallback
4. Implement retry logic with exponential backoff
5. Monitor the server logs for disconnection patterns
